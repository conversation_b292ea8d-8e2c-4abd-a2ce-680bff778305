//! Web服务器实现

use tokio::net::TcpListener;
use tower::ServiceBuilder;
use tower_http::{cors::CorsLayer, trace::TraceLayer};
use tracing::info;
use sigmax_core::SigmaXResult;
use crate::{routes::create_routes, state::AppState};
use std::sync::Arc;

/// Web服务器配置
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct WebServerConfig {
    pub host: String,
    pub port: u16,
    pub database_url: String,
}

impl Default for WebServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            database_url: "".to_string(),
        }
    }
}

/// Web服务器
pub struct WebServer {
    config: WebServerConfig,
    app_state: Option<AppState>,
}

impl WebServer {
    /// 创建新的Web服务器
    pub fn new(config: WebServerConfig) -> Self {
        Self {
            config,
            app_state: None,
        }
    }

    /// 创建带有默认配置的Web服务器
    pub fn with_port(port: u16) -> Self {
        let mut config = WebServerConfig::default();
        config.port = port;
        Self {
            config,
            app_state: None,
        }
    }

    /// 初始化系统配置缓存
    ///
    /// 这个方法应该在启动服务器之前调用
    pub async fn initialize_system_configs(&mut self) -> SigmaXResult<()> {
        info!("🔧 Initializing system configurations...");

        // 初始化应用状态
        let state = AppState::new(&self.config.database_url).await?;

        // 并行初始化所有配置服务
        info!("⚡ Starting parallel configuration initialization...");
        state.config_manager.initialize_all().await
            .map_err(|e| sigmax_core::SigmaXError::Config(format!("Failed to initialize config manager: {}", e)))?;

        // 初始化全局配置访问
        info!("🌐 Initializing global configuration access...");
        crate::services::config::init_global_config_manager(state.config_manager.clone())
            .map_err(|e| sigmax_core::SigmaXError::Config(e))?;

        // 保存应用状态
        self.app_state = Some(state);

        info!("✅ System configurations initialized successfully");
        Ok(())
    }

    /// 获取配置管理器（用于全局访问）
    pub fn get_config_manager(&self) -> Option<Arc<crate::services::ConfigManager>> {
        self.app_state.as_ref().map(|state| state.config_manager.clone())
    }

    /// 启动服务器
    pub async fn start(&mut self) -> SigmaXResult<()> {
        // 使用已初始化的应用状态，如果没有则创建新的
        let state = match self.app_state.take() {
            Some(state) => {
                info!("Using pre-initialized application state");
                state
            }
            None => {
                info!("Creating new application state");
                AppState::new(&self.config.database_url).await?
            }
        };

        // 启动WebSocket事件循环
        state.websocket_server.start_event_loop().await?;
        info!("WebSocket event loop started");

        // 创建应用
        let app = create_routes()
            .with_state(state)
            .layer(
                ServiceBuilder::new()
                    .layer(TraceLayer::new_for_http())
                    .layer(CorsLayer::permissive())
            );

        let addr = format!("{}:{}", self.config.host, self.config.port);
        let listener = TcpListener::bind(&addr).await
            .map_err(|e| sigmax_core::SigmaXError::network_error("server_bind", 5000))?;

        info!("Web server starting on {}", addr);

        axum::serve(listener, app).await
            .map_err(|e| sigmax_core::SigmaXError::network_error("server_start", 5000))?;

        Ok(())
    }
}

/// 创建应用状态
pub async fn create_app_state(config: sigmax_core::AppConfig) -> SigmaXResult<AppState> {
    AppState::new(&config.database.url).await
}

/// 启动服务器
pub async fn start_server(state: AppState, host: &str, port: u16) -> SigmaXResult<()> {
    // 启动WebSocket事件循环
    state.websocket_server.start_event_loop().await?;
    info!("WebSocket event loop started");

    // 创建应用
    let app = create_routes()
        .with_state(state)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
        );

    let addr = format!("{}:{}", host, port);
    let listener = TcpListener::bind(&addr).await
        .map_err(|e| sigmax_core::SigmaXError::network_error("graceful_shutdown", 30000))?;

    info!("Web server listening on {}", addr);

    axum::serve(listener, app).await
        .map_err(|e| sigmax_core::SigmaXError::network_error("shutdown_timeout", 30000))?;

    Ok(())
}
