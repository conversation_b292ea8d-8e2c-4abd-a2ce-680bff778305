//! API处理器模块

pub mod basic_handlers;
pub mod strategy_handlers;
pub mod strategy_validation_handlers;

// 注意：risk_config_handlers 已删除，功能已迁移到统一风险管理API
pub mod backtest_data;
pub mod backtest_results;
pub mod execution_handlers;

// 统一风险管理API处理器
pub mod risk_handlers;

// 第二阶段：投资组合与订单管理API处理器
pub mod portfolio_handlers;
pub mod order_handlers;
pub mod position_handlers;

// 第三阶段：数据管理与性能监控API处理器
pub mod data_management_handlers;
pub mod performance_monitoring_handlers;
pub mod cache_management_handlers;
pub mod cache_config_handlers;
pub mod database_management_handlers;

// 第四阶段：交易所集成与报告生成API处理器
pub mod exchange_handlers;
pub mod market_data_handlers;
pub mod report_handlers;
pub mod system_config_handlers;

// 第五阶段：WebSocket管理API处理器
pub mod websocket_handlers;
pub mod realtime_handlers;

// 策略模板管理API处理器
pub mod strategy_template_handlers;



// 重新导出常用的处理器
pub use basic_handlers::*;
pub use strategy_handlers::*;
pub use strategy_validation_handlers::*;

// 注意：risk_config_handlers 已删除，功能已迁移到统一风险管理API
pub use backtest_data::*;
pub use backtest_results::*;
pub use execution_handlers::*;

// 重新导出风险管理处理器
pub use risk_handlers::*;

// 重新导出投资组合与订单管理处理器
pub use portfolio_handlers::*;
pub use order_handlers::*;
pub use position_handlers::*;

// 重新导出数据管理与性能监控处理器
pub use data_management_handlers::*;
pub use performance_monitoring_handlers::*;
pub use cache_management_handlers::*;
pub use cache_config_handlers::*;
pub use database_management_handlers::*;

// 重新导出交易所集成与报告生成处理器
pub use exchange_handlers::*;
pub use market_data_handlers::*;
pub use report_handlers::*;
pub use system_config_handlers::*;
 
// 重新导出WebSocket管理处理器
pub use websocket_handlers::*;
pub use realtime_handlers::*;

// 重新导出策略模板处理器
pub use strategy_template_handlers::*;
