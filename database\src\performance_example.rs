//! 性能监控使用示例
//!
//! 展示如何在 SigmaX 项目中使用数据库性能监控功能

use std::sync::Arc;
use std::time::Duration;
use crate::database::DatabaseManager;
use crate::performance::{PerformanceConfig, PerformanceMonitor};
use sigmax_core::{SigmaXResult, DatabaseConfig};

/// 性能监控使用示例
pub struct PerformanceExample {
    db_manager: DatabaseManager,
}

impl PerformanceExample {
    /// 创建新的示例实例
    pub async fn new() -> SigmaXResult<Self> {
        // 创建数据库配置
        let db_config = DatabaseConfig {
            url: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require".to_string(),
            max_connections: 10,
            connection_timeout: Duration::from_secs(30),
            idle_timeout: Some(Duration::from_secs(600)),
            auto_migrate: false,
        };

        // 创建数据库管理器
        let mut db_manager = DatabaseManager::new(db_config).await?;

        // 配置性能监控
        let perf_config = PerformanceConfig {
            enabled: true,
            slow_query_threshold_ms: 500, // 500ms 作为慢查询阈值
            log_all_queries: true,
            log_raw_sql_in_dev: true,
            metrics_retention_days: 7,
            slow_query_retention_days: 3,
            refresh_interval_seconds: 30,
        };

        // 启用性能监控
        db_manager.enable_performance_monitoring(perf_config);

        Ok(Self { db_manager })
    }

    /// 演示基本的性能监控功能
    pub async fn demonstrate_basic_monitoring(&self) -> SigmaXResult<()> {
        println!("🔍 演示基本性能监控功能...");

        // 执行一些查询操作
        self.simulate_database_operations().await?;

        // 获取性能统计
        if let Some(stats) = self.db_manager.get_performance_stats().await? {
            println!("📊 性能统计报告:");
            println!("  总查询数: {}", stats.total_queries);
            println!("  慢查询数: {}", stats.slow_queries_count);
            println!("  错误查询数: {}", stats.error_queries_count);
            println!("  平均查询时间: {:.2}ms", stats.avg_query_duration_ms);
            
            println!("  查询类型分布:");
            for (query_type, count) in &stats.query_type_distribution {
                println!("    {:?}: {}", query_type, count);
            }

            println!("  连接池状态:");
            println!("    总连接数: {}", stats.connection_pool_status.total_connections);
            println!("    活跃连接数: {}", stats.connection_pool_status.active_connections);
            println!("    空闲连接数: {}", stats.connection_pool_status.idle_connections);
        }

        Ok(())
    }

    /// 演示慢查询监控
    pub async fn demonstrate_slow_query_monitoring(&self) -> SigmaXResult<()> {
        println!("🐌 演示慢查询监控...");

        // 模拟一个慢查询
        let _ = self.db_manager.execute_with_monitoring(
            "slow_query_simulation",
            async {
                // 模拟慢查询（实际项目中这会是真实的数据库查询）
                tokio::time::sleep(Duration::from_millis(600)).await;
                Ok::<(), sigmax_core::SigmaXError>(())
            }
        ).await;

        // 获取慢查询记录
        let slow_queries = self.db_manager.get_slow_queries(Some(10));
        
        println!("📋 慢查询记录 (最近10条):");
        for (i, query) in slow_queries.iter().enumerate() {
            println!("  {}. 查询ID: {}", i + 1, query.id);
            println!("     执行时间: {}ms", query.duration_ms);
            println!("     执行时间: {}", query.executed_at.format("%Y-%m-%d %H:%M:%S"));
            if let Some(error) = &query.error {
                println!("     错误: {}", error);
            }
            println!();
        }

        Ok(())
    }

    /// 演示查询性能指标
    pub async fn demonstrate_query_metrics(&self) -> SigmaXResult<()> {
        println!("📈 演示查询性能指标...");

        // 获取查询性能指标
        let metrics = self.db_manager.get_query_metrics();
        
        println!("🎯 查询性能指标:");
        for metric in &metrics {
            println!("  查询哈希: {}", metric.query_hash);
            println!("  查询类型: {:?}", metric.query_type);
            println!("  执行次数: {}", metric.execution_count);
            println!("  平均执行时间: {:.2}ms", metric.avg_duration_ms);
            println!("  最小执行时间: {}ms", metric.min_duration_ms);
            println!("  最大执行时间: {}ms", metric.max_duration_ms);
            println!("  错误次数: {}", metric.error_count);
            println!("  最后执行时间: {}", metric.last_executed_at.format("%Y-%m-%d %H:%M:%S"));
            println!();
        }

        Ok(())
    }

    /// 演示性能数据清理
    pub async fn demonstrate_cleanup(&self) -> SigmaXResult<()> {
        println!("🧹 演示性能数据清理...");

        // 清理过期的性能数据
        self.db_manager.cleanup_performance_data().await?;
        
        println!("✅ 性能数据清理完成");

        Ok(())
    }

    /// 模拟数据库操作
    async fn simulate_database_operations(&self) -> SigmaXResult<()> {
        // 模拟各种类型的查询
        let operations = vec![
            ("SELECT * FROM orders LIMIT 10", 50),
            ("INSERT INTO orders (...) VALUES (...)", 100),
            ("UPDATE orders SET status = 'filled' WHERE id = ?", 75),
            ("DELETE FROM orders WHERE created_at < ?", 200),
            ("SELECT COUNT(*) FROM strategies", 25),
        ];

        for (query_name, duration_ms) in operations {
            let _ = self.db_manager.execute_with_monitoring(
                query_name,
                async move {
                    // 模拟查询执行时间
                    tokio::time::sleep(Duration::from_millis(duration_ms)).await;
                    Ok::<(), sigmax_core::SigmaXError>(())
                }
            ).await;
        }

        // 模拟一个失败的查询
        let _ = self.db_manager.execute_with_monitoring(
            "failing_query",
            async {
                Err(sigmax_core::SigmaXError::database_error("模拟查询失败".to_string()))
            }
        ).await;

        Ok(())
    }

    /// 运行完整的性能监控演示
    pub async fn run_full_demonstration(&self) -> SigmaXResult<()> {
        println!("🚀 开始性能监控完整演示...\n");

        // 基本监控功能
        self.demonstrate_basic_monitoring().await?;
        println!();

        // 慢查询监控
        self.demonstrate_slow_query_monitoring().await?;
        println!();

        // 查询性能指标
        self.demonstrate_query_metrics().await?;
        println!();

        // 数据清理
        self.demonstrate_cleanup().await?;
        println!();

        println!("✅ 性能监控演示完成！");

        Ok(())
    }
}

/// 性能监控最佳实践指南
pub struct PerformanceBestPractices;

impl PerformanceBestPractices {
    /// 打印性能监控最佳实践
    pub fn print_best_practices() {
        println!("📚 数据库性能监控最佳实践:");
        println!();
        
        println!("1. 🎯 监控配置:");
        println!("   - 根据业务需求设置合适的慢查询阈值");
        println!("   - 在生产环境中关闭原始SQL记录");
        println!("   - 定期清理过期的监控数据");
        println!();
        
        println!("2. 🔍 查询优化:");
        println!("   - 定期检查慢查询报告");
        println!("   - 为频繁查询的字段添加索引");
        println!("   - 避免在循环中执行数据库查询");
        println!();
        
        println!("3. 📊 连接池管理:");
        println!("   - 监控连接池使用情况");
        println!("   - 根据负载调整连接池大小");
        println!("   - 设置合适的连接超时时间");
        println!();
        
        println!("4. 🚨 告警设置:");
        println!("   - 为慢查询数量设置告警阈值");
        println!("   - 监控错误查询比例");
        println!("   - 关注连接池耗尽情况");
        println!();
        
        println!("5. 📈 性能分析:");
        println!("   - 定期分析查询类型分布");
        println!("   - 识别性能瓶颈和优化机会");
        println!("   - 跟踪性能趋势变化");
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_performance_monitoring_setup() {
        // 注意：这个测试需要真实的数据库连接
        // 在实际项目中，你可能需要使用测试数据库
        
        let config = PerformanceConfig::default();
        assert!(config.enabled);
        assert_eq!(config.slow_query_threshold_ms, 1000);
        assert_eq!(config.metrics_retention_days, 30);
    }

    #[test]
    fn test_best_practices_display() {
        // 测试最佳实践指南的显示
        PerformanceBestPractices::print_best_practices();
        // 这个测试主要是确保代码能够正常运行
    }
}
