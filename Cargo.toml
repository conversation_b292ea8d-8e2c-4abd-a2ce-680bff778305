[workspace]
resolver = "2"
members = [
    "bin",
    "core",
    "data",
    "exchange",
    "portfolio",
    "execution",
    "risk",
    "strategies",
    "engines",
    "reporting",
    "database",
    "performance_monitor",
    "web",
    "trading_analysis",
    # 新架构模块
    "interfaces",
    "services",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["SigmaX Team"]
license = "MIT"
repository = "https://github.com/your-org/SigmaX"

# 编译优化配置
[profile.dev]
# 减少debug信息以节省空间
debug = 1  # 0 = 无debug信息, 1 = 行号信息, 2 = 完整debug信息
# 启用增量编译
incremental = true
# 减少代码生成单元数量
codegen-units = 256

[profile.release]
# 发布版本优化
debug = false
lto = true
codegen-units = 1
panic = "abort"

[workspace.dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }
# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }
# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "rust_decimal"] }
diesel = { version = "2.1", features = ["postgres", "chrono", "uuid", "numeric", "r2d2"] }
diesel_migrations = "2.1"
# Redis缓存
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
# 错误处理
anyhow = "1.0"
thiserror = "1.0"
# 时间处理
chrono = { version = "0.4", features = ["serde"] }
# 唯一标识符
uuid = { version = "1.0", features = ["v4", "serde"] }
# 日志
tracing = "0.1"
tracing-subscriber = "0.3"
# 数值计算
rust_decimal = { version = "1.0", features = ["serde"] }
# 配置管理
config = "0.13"
# Web框架
axum = { version = "0.7", features = ["ws"] }
tower = "0.4"
# 测试
mockall = "0.11"
# 异步trait
async-trait = "0.1"
# 数据验证
validator = { version = "0.16", features = ["derive"] }

# 内部依赖
sigmax-core = { path = "core" }
sigmax-data = { path = "data" }
sigmax-exchange = { path = "exchange" }
sigmax-portfolio = { path = "portfolio" }
sigmax-execution = { path = "execution" }
sigmax-risk = { path = "risk" }
sigmax-strategies = { path = "strategies" }
sigmax-engines = { path = "engines" }
sigmax-reporting = { path = "reporting" }
sigmax-database = { path = "database" }
sigmax-performance-monitor = { path = "performance_monitor" }
sigmax-web = { path = "web" }
# 新架构模块
sigmax-interfaces = { path = "interfaces" }
sigmax-services = { path = "services" }


