//! 核心模块常量定义
//!
//! 统一管理核心业务逻辑相关的常量，避免魔法数字

/// 配置验证常量
pub mod config {
    /// 数据库连接池配置
    pub mod database {
        /// 最小连接数
        pub const MIN_CONNECTIONS: u32 = 1;
        
        /// 最大连接数
        pub const MAX_CONNECTIONS: u32 = 100;
        
        /// 默认连接数
        pub const DEFAULT_CONNECTIONS: u32 = 10;
        
        /// 连接超时时间 (秒)
        pub const CONNECTION_TIMEOUT_SECONDS: u64 = 30;
        
        /// 空闲超时时间 (秒)
        pub const IDLE_TIMEOUT_SECONDS: u64 = 600;
    }
    
    /// Web服务器配置
    pub mod web {
        /// 最小端口号
        pub const MIN_PORT: u16 = 1024;
        
        /// 最大端口号
        pub const MAX_PORT: u16 = 65535;
        
        /// 默认端口号
        pub const DEFAULT_PORT: u16 = 8080;
        
        /// 默认主机地址
        pub const DEFAULT_HOST: &str = "0.0.0.0";
        
        /// 请求超时时间 (秒)
        pub const REQUEST_TIMEOUT_SECONDS: u64 = 30;
        
        /// 最大请求体大小 (字节)
        pub const MAX_REQUEST_SIZE_BYTES: usize = 16 * 1024 * 1024; // 16MB
    }
    
    /// 缓存配置
    pub mod cache {
        /// 默认TTL (秒)
        pub const DEFAULT_TTL_SECONDS: u64 = 300;
        
        /// 最大TTL (秒)
        pub const MAX_TTL_SECONDS: u64 = 86400; // 24小时
        
        /// 默认容量
        pub const DEFAULT_CAPACITY: usize = 1000;
        
        /// 最大容量
        pub const MAX_CAPACITY: usize = 1000000;
        
        /// 清理间隔 (毫秒)
        pub const CLEANUP_INTERVAL_MS: u64 = 60000;
        
        /// 内存使用阈值 (百分比)
        pub const MEMORY_THRESHOLD_PERCENT: f64 = 80.0;
    }
    
    /// 监控配置
    pub mod monitoring {
        /// 默认指标收集间隔 (秒)
        pub const DEFAULT_METRICS_INTERVAL_SECONDS: u64 = 60;
        
        /// 最小指标收集间隔 (秒)
        pub const MIN_METRICS_INTERVAL_SECONDS: u64 = 1;
        
        /// 最大指标收集间隔 (秒)
        pub const MAX_METRICS_INTERVAL_SECONDS: u64 = 3600;
        
        /// 默认健康检查间隔 (秒)
        pub const DEFAULT_HEALTH_CHECK_INTERVAL_SECONDS: u64 = 30;
        
        /// 默认日志级别
        pub const DEFAULT_LOG_LEVEL: &str = "info";
        
        /// 最大日志文件大小 (MB)
        pub const MAX_LOG_FILE_SIZE_MB: u64 = 100;
        
        /// 最大日志文件数量
        pub const MAX_LOG_FILES: u32 = 10;
    }
}

/// 交易相关常量
pub mod trading {
    /// 订单配置
    pub mod order {
        /// 最小订单金额
        pub const MIN_ORDER_AMOUNT: f64 = 0.001;
        
        /// 最大订单金额
        pub const MAX_ORDER_AMOUNT: f64 = 1000000.0;
        
        /// 默认订单超时时间 (秒)
        pub const DEFAULT_ORDER_TIMEOUT_SECONDS: u64 = 300;
        
        /// 最大订单超时时间 (秒)
        pub const MAX_ORDER_TIMEOUT_SECONDS: u64 = 3600;
    }
    
    /// 风险管理配置
    pub mod risk {
        /// 默认最大持仓比例 (百分比)
        pub const DEFAULT_MAX_POSITION_PERCENT: f64 = 10.0;
        
        /// 最大持仓比例上限 (百分比)
        pub const MAX_POSITION_PERCENT_LIMIT: f64 = 50.0;
        
        /// 默认止损比例 (百分比)
        pub const DEFAULT_STOP_LOSS_PERCENT: f64 = 5.0;
        
        /// 最大止损比例 (百分比)
        pub const MAX_STOP_LOSS_PERCENT: f64 = 20.0;
        
        /// 默认最大日交易次数
        pub const DEFAULT_MAX_DAILY_TRADES: u32 = 100;
        
        /// 最大日交易次数上限
        pub const MAX_DAILY_TRADES_LIMIT: u32 = 1000;
    }
}

/// 性能相关常量
pub mod performance {
    /// 线程池配置
    pub mod thread_pool {
        /// 最小线程数
        pub const MIN_THREADS: usize = 1;
        
        /// 最大线程数
        pub const MAX_THREADS: usize = 1000;
        
        /// 默认线程数 (CPU核心数的2倍)
        pub const DEFAULT_THREADS_MULTIPLIER: usize = 2;
    }
    
    /// 内存配置
    pub mod memory {
        /// 默认缓冲区大小 (字节)
        pub const DEFAULT_BUFFER_SIZE: usize = 8192;
        
        /// 最大缓冲区大小 (字节)
        pub const MAX_BUFFER_SIZE: usize = 1024 * 1024; // 1MB
        
        /// 内存警告阈值 (百分比)
        pub const MEMORY_WARNING_THRESHOLD: f64 = 80.0;
        
        /// 内存错误阈值 (百分比)
        pub const MEMORY_ERROR_THRESHOLD: f64 = 95.0;
    }
}

/// 时间相关常量
pub mod time {
    /// 时间格式
    pub const ISO8601_FORMAT: &str = "%Y-%m-%dT%H:%M:%S%.3fZ";
    
    /// 默认时区
    pub const DEFAULT_TIMEZONE: &str = "UTC";
    
    /// 一秒的毫秒数
    pub const MILLISECONDS_PER_SECOND: u64 = 1000;
    
    /// 一分钟的秒数
    pub const SECONDS_PER_MINUTE: u64 = 60;
    
    /// 一小时的秒数
    pub const SECONDS_PER_HOUR: u64 = 3600;
    
    /// 一天的秒数
    pub const SECONDS_PER_DAY: u64 = 86400;
}

/// 网络相关常量
pub mod network {
    /// HTTP状态码
    pub mod http {
        /// 成功状态码
        pub const OK: u16 = 200;
        
        /// 创建成功状态码
        pub const CREATED: u16 = 201;
        
        /// 无内容状态码
        pub const NO_CONTENT: u16 = 204;
        
        /// 错误请求状态码
        pub const BAD_REQUEST: u16 = 400;
        
        /// 未授权状态码
        pub const UNAUTHORIZED: u16 = 401;
        
        /// 禁止访问状态码
        pub const FORBIDDEN: u16 = 403;
        
        /// 未找到状态码
        pub const NOT_FOUND: u16 = 404;
        
        /// 内部服务器错误状态码
        pub const INTERNAL_SERVER_ERROR: u16 = 500;
    }
    
    /// 超时配置
    pub mod timeout {
        /// 默认连接超时 (毫秒)
        pub const DEFAULT_CONNECT_TIMEOUT_MS: u64 = 5000;
        
        /// 默认读取超时 (毫秒)
        pub const DEFAULT_READ_TIMEOUT_MS: u64 = 30000;
        
        /// 默认写入超时 (毫秒)
        pub const DEFAULT_WRITE_TIMEOUT_MS: u64 = 30000;
    }
}
