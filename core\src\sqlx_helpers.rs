//! SQLx编译时检查助手模块 - 完整实现
//!
//! 这个模块提供了与实际数据库schema匹配的SQLx编译时检查功能。
//!
//! # 核心改进
//! 1. **正确的schema映射**：使用实际的数据库字段和类型
//! 2. **PostgreSQL enum支持**：正确处理order_type, order_side, order_status
//! 3. **关联查询**：正确处理trading_pairs和exchanges的JOIN
//! 4. **类型安全**：完整的编译时类型检查
//!
//! # 数据库Schema匹配
//! - orders表使用trading_pair_id (int4) 和 exchange_id (int4)
//! - 需要JOIN trading_pairs和exchanges表获取详细信息
//! - 使用PostgreSQL enum类型而不是字符串

use sqlx::{PgPool, Type, FromRow};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::{SigmaXResult, SigmaXError, OrderSide, OrderType, OrderStatus, TradingPair, ExchangeId};

/// PostgreSQL enum类型映射
///
/// 这些结构体映射到数据库中的PostgreSQL enum类型，
/// 确保SQLx能够正确处理enum值的序列化和反序列化

#[derive(Debug, Clone, Copy, PartialEq, Eq, Type)]
#[sqlx(type_name = "order_side", rename_all = "PascalCase")]
pub enum PgOrderSide {
    Buy,
    Sell,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Type)]
#[sqlx(type_name = "order_type", rename_all = "PascalCase")]
pub enum PgOrderType {
    Market,
    Limit,
    StopLoss,
    StopLimit,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Type)]
#[sqlx(type_name = "order_status", rename_all = "PascalCase")]
pub enum PgOrderStatus {
    Pending,
    Filled,
    PartiallyFilled,
    Cancelled,
    Rejected,
    Expired,
}

/// 类型转换实现
impl From<OrderSide> for PgOrderSide {
    fn from(side: OrderSide) -> Self {
        match side {
            OrderSide::Buy => PgOrderSide::Buy,
            OrderSide::Sell => PgOrderSide::Sell,
        }
    }
}

impl From<PgOrderSide> for OrderSide {
    fn from(side: PgOrderSide) -> Self {
        match side {
            PgOrderSide::Buy => OrderSide::Buy,
            PgOrderSide::Sell => OrderSide::Sell,
        }
    }
}

impl From<OrderType> for PgOrderType {
    fn from(order_type: OrderType) -> Self {
        match order_type {
            OrderType::Market => PgOrderType::Market,
            OrderType::Limit => PgOrderType::Limit,
            OrderType::StopLoss => PgOrderType::StopLoss,
            OrderType::StopLimit => PgOrderType::StopLimit,
        }
    }
}

impl From<PgOrderType> for OrderType {
    fn from(order_type: PgOrderType) -> Self {
        match order_type {
            PgOrderType::Market => OrderType::Market,
            PgOrderType::Limit => OrderType::Limit,
            PgOrderType::StopLoss => OrderType::StopLoss,
            PgOrderType::StopLimit => OrderType::StopLimit,
        }
    }
}

impl From<OrderStatus> for PgOrderStatus {
    fn from(status: OrderStatus) -> Self {
        match status {
            OrderStatus::Pending => PgOrderStatus::Pending,
            OrderStatus::Filled => PgOrderStatus::Filled,
            OrderStatus::PartiallyFilled => PgOrderStatus::PartiallyFilled,
            OrderStatus::Cancelled => PgOrderStatus::Cancelled,
            OrderStatus::Rejected => PgOrderStatus::Rejected,
            OrderStatus::Expired => PgOrderStatus::Expired,
        }
    }
}

impl From<PgOrderStatus> for OrderStatus {
    fn from(status: PgOrderStatus) -> Self {
        match status {
            PgOrderStatus::Pending => OrderStatus::Pending,
            PgOrderStatus::Filled => OrderStatus::Filled,
            PgOrderStatus::PartiallyFilled => OrderStatus::PartiallyFilled,
            PgOrderStatus::Cancelled => OrderStatus::Cancelled,
            PgOrderStatus::Rejected => OrderStatus::Rejected,
            PgOrderStatus::Expired => OrderStatus::Expired,
        }
    }
}

/// 正确的Order查询结果结构
///
/// 这个结构体匹配实际的数据库schema，包括正确的字段名和类型
#[derive(Debug, FromRow)]
pub struct OrderQueryResult {
    // 订单基本信息
    pub id: Uuid,
    pub strategy_id: Option<Uuid>,
    pub trading_pair_id: i32,
    pub exchange_id: i32,
    pub exchange_order_id: Option<String>,
    pub parent_order_id: Option<Uuid>,

    // 订单详情
    pub order_type: PgOrderType,
    pub side: PgOrderSide,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub stop_price: Option<Decimal>,
    pub status: PgOrderStatus,
    pub filled_quantity: Decimal,
    pub average_price: Option<Decimal>,

    // 时间戳
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub submitted_at: Option<DateTime<Utc>>,
    pub filled_at: Option<DateTime<Utc>>,
    pub cancelled_at: Option<DateTime<Utc>>,

    // 关联表信息（通过JOIN获取）
    pub trading_pair_symbol: String,
    pub trading_pair_base: String,
    pub trading_pair_quote: String,
    pub exchange_name: String,
}

impl OrderQueryResult {
    /// 转换为核心Order模型
    pub fn into_order(self) -> crate::Order {
        crate::Order {
            id: self.id,
            strategy_id: self.strategy_id,
            exchange_id: ExchangeId::from(self.exchange_name),
            trading_pair: TradingPair::new(self.trading_pair_base, self.trading_pair_quote),
            side: self.side.into(),
            order_type: self.order_type.into(),
            quantity: self.quantity,
            price: self.price,
            stop_price: self.stop_price,
            status: self.status.into(),
            filled_quantity: self.filled_quantity,
            average_price: self.average_price,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}

/// 正确的Trade查询结果结构
#[derive(Debug, FromRow)]
pub struct TradeQueryResult {
    // 交易基本信息
    pub id: Uuid,
    pub order_id: Uuid,
    pub trading_pair_id: i32,
    pub exchange_id: i32,
    pub exchange_trade_id: Option<String>,

    // 交易详情
    pub side: PgOrderSide,
    pub quantity: Decimal,
    pub price: Decimal,
    pub quote_quantity: Decimal, // 计算字段
    pub fee: Decimal,
    pub fee_asset: Option<String>,
    pub commission_rate: Option<Decimal>,
    pub is_maker: Option<bool>,

    // 时间戳
    pub trade_time: Option<DateTime<Utc>>,
    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,

    // 关联表信息（通过JOIN获取）
    pub trading_pair_symbol: String,
    pub trading_pair_base: String,
    pub trading_pair_quote: String,
    pub exchange_name: String,
}

impl TradeQueryResult {
    /// 转换为核心Trade模型
    pub fn into_trade(self) -> crate::Trade {
        crate::Trade {
            id: self.id,
            order_id: self.order_id,
            exchange_id: ExchangeId::from(self.exchange_name),
            trading_pair: TradingPair::new(self.trading_pair_base, self.trading_pair_quote),
            side: self.side.into(),
            quantity: self.quantity,
            price: self.price,
            fee: self.fee,
            fee_asset: self.fee_asset,
            executed_at: self.executed_at,
            created_at: self.created_at,
        }
    }
}

/// SQLx查询助手 - 完整实现版本
///
/// 这个结构体展示了如何封装SQLx功能，提供类型安全的数据库操作
pub struct SqlxQueryHelper {
    pool: PgPool,
}

impl SqlxQueryHelper {
    /// 创建新的SQLx查询助手
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取数据库连接池的引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 演示：简单的编译时检查查询
    ///
    /// 这个方法展示了SQLx编译时检查的基本概念
    /// 注意：实际实现需要根据真实的数据库schema调整
    pub async fn demo_compile_time_check(&self) -> SigmaXResult<i64> {
        // 这是一个简单的演示查询，展示编译时检查
        let result = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM orders"
        )
        .fetch_one(&self.pool)
        .await;

        match result {
            Ok(count) => Ok(count.unwrap_or(0)),
            Err(e) => Err(SigmaXError::database_error(format!("Query failed: {}", e))),
        }
    }

    /// 演示：参数化查询的编译时检查
    pub async fn demo_parameterized_query(&self, order_id: Uuid) -> SigmaXResult<bool> {
        let result = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM orders WHERE id = $1)",
            order_id
        )
        .fetch_one(&self.pool)
        .await;

        match result {
            Ok(exists) => Ok(exists.unwrap_or(false)),
            Err(e) => Err(SigmaXError::database_error(format!("Query failed: {}", e))),
        }
    }
}

/// Order相关的SQLx查询实现 - 修复版本
///
/// # 重要改进
/// 这个实现修复了之前发现的所有schema不匹配问题：
/// 1. ✅ 使用正确的字段名：trading_pair_id, exchange_id
/// 2. ✅ 正确处理PostgreSQL enum类型
/// 3. ✅ 使用JOIN查询获取关联信息
/// 4. ✅ 完整的编译时类型检查
impl SqlxQueryHelper {

    /// 保存订单到数据库（修复版本）
    ///
    /// # 编译时安全 ✅
    /// 🎯 现在使用真正的SQLx编译时检查！
    /// - 正确的数据库schema验证
    /// - PostgreSQL enum类型检查
    /// - 字段类型匹配验证
    /// - SQL语法正确性检查
    pub async fn save_order_fixed(&self, order: &crate::Order) -> SigmaXResult<()> {
        // 首先获取trading_pair_id和exchange_id
        let trading_pair_id = self.get_trading_pair_id(&order.trading_pair.symbol()).await?;
        let exchange_id = self.get_exchange_id(&order.exchange_id.to_string()).await?;

        // 🔥 真正的SQLx编译时检查！
        // 这个查询现在会在编译时验证：
        // 1. 表结构是否正确
        // 2. 字段名是否存在
        // 3. 类型是否匹配
        // 4. SQL语法是否正确
        let result = sqlx::query!(
            r#"
            INSERT INTO orders (
                id, strategy_id, trading_pair_id, exchange_id,
                order_type, side, quantity, price, stop_price, status,
                filled_quantity, average_price, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
            )
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                filled_quantity = EXCLUDED.filled_quantity,
                average_price = EXCLUDED.average_price,
                updated_at = EXCLUDED.updated_at
            "#,
            order.id,
            order.strategy_id,
            trading_pair_id,
            exchange_id,
            PgOrderType::from(order.order_type) as PgOrderType,
            PgOrderSide::from(order.side) as PgOrderSide,
            order.quantity,
            order.price,
            order.stop_price,
            PgOrderStatus::from(order.status) as PgOrderStatus,
            order.filled_quantity,
            order.average_price,
            order.created_at,
            order.updated_at
        )
        .execute(&self.pool)
        .await;

        match result {
            Ok(_) => Ok(()),
            Err(e) => Err(SigmaXError::database_error(format!("Failed to save order: {}", e))),
        }
    }

    /// 从数据库加载订单（修复版本）
    ///
    /// # 类型安全
    /// 使用JOIN查询和强类型结果结构
    pub async fn load_order_fixed(&self, id: Uuid) -> SigmaXResult<Option<crate::Order>> {
        let result = sqlx::query_as!(
            OrderQueryResult,
            r#"
            SELECT
                o.id, o.strategy_id, o.trading_pair_id, o.exchange_id,
                o.exchange_order_id, o.parent_order_id,
                o.order_type as "order_type: PgOrderType",
                o.side as "side: PgOrderSide",
                o.quantity, o.price, o.stop_price,
                o.status as "status: PgOrderStatus",
                o.filled_quantity, o.average_price,
                o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                tp.symbol as trading_pair_symbol,
                tp.base_asset as trading_pair_base,
                tp.quote_asset as trading_pair_quote,
                e.name as exchange_name
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            JOIN exchanges e ON o.exchange_id = e.id
            WHERE o.id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await;

        match result {
            Ok(Some(order_result)) => Ok(Some(order_result.into_order())),
            Ok(None) => Ok(None),
            Err(e) => Err(SigmaXError::database_error(format!("Failed to load order: {}", e))),
        }
    }

    /// 辅助方法：获取trading_pair_id
    async fn get_trading_pair_id(&self, symbol: &str) -> SigmaXResult<i32> {
        let result = sqlx::query_scalar!(
            "SELECT id FROM trading_pairs WHERE symbol = $1",
            symbol
        )
        .fetch_optional(&self.pool)
        .await;

        match result {
            Ok(Some(id)) => Ok(id),
            Ok(None) => Err(SigmaXError::InvalidParameter(format!("Trading pair not found: {}", symbol))),
            Err(e) => Err(SigmaXError::database_error(format!("Failed to get trading pair ID: {}", e))),
        }
    }

    /// 辅助方法：获取exchange_id
    async fn get_exchange_id(&self, name: &str) -> SigmaXResult<i32> {
        let result = sqlx::query_scalar!(
            "SELECT id FROM exchanges WHERE name = $1",
            name
        )
        .fetch_optional(&self.pool)
        .await;

        match result {
            Ok(Some(id)) => Ok(id),
            Ok(None) => Err(SigmaXError::InvalidParameter(format!("Exchange not found: {}", name))),
            Err(e) => Err(SigmaXError::database_error(format!("Failed to get exchange ID: {}", e))),
        }
    }
}
