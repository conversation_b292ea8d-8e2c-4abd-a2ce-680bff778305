//! 错误处理扩展功能
//! 
//! 在核心错误系统基础上，按需提供高级功能
//! 遵循"渐进式复杂度"原则

use super::minimal_core::{CoreError, CoreResult, SimpleError, Category, Severity};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};

/// 错误上下文 - 可选的详细信息
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct ErrorContext {
    /// 模块名
    pub module: Option<&'static str>,
    /// 函数名
    pub function: Option<&'static str>,
    /// 用户ID
    pub user_id: Option<uuid::Uuid>,
    /// 请求ID
    pub request_id: Option<String>,
    /// 自定义元数据
    pub metadata: HashMap<String, String>,
}

impl ErrorContext {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn with_module(mut self, module: &'static str) -> Self {
        self.module = Some(module);
        self
    }
    
    pub fn with_function(mut self, function: &'static str) -> Self {
        self.function = Some(function);
        self
    }
    
    pub fn with_user_id(mut self, user_id: uuid::Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }
    
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
    
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 增强错误 - 包含上下文信息
#[derive(Debug)]
pub struct ContextualError {
    pub error: SimpleError,
    pub context: ErrorContext,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ContextualError {
    pub fn new(error: SimpleError, context: ErrorContext) -> Self {
        Self {
            error,
            context,
            timestamp: chrono::Utc::now(),
        }
    }
    
    /// 从简单错误创建
    pub fn from_simple(error: SimpleError) -> Self {
        Self::new(error, ErrorContext::new())
    }
}

impl std::fmt::Display for ContextualError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.error)?;
        
        if let Some(module) = self.context.module {
            write!(f, " [module: {}]", module)?;
        }
        
        if let Some(function) = self.context.function {
            write!(f, " [function: {}]", function)?;
        }
        
        if let Some(user_id) = self.context.user_id {
            write!(f, " [user: {}]", user_id)?;
        }
        
        Ok(())
    }
}

impl std::error::Error for ContextualError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.error)
    }
}

impl CoreError for ContextualError {
    fn category(&self) -> Category {
        self.error.category()
    }
    
    fn severity(&self) -> Severity {
        self.error.severity()
    }
    
    fn error_code(&self) -> &'static str {
        self.error.error_code()
    }
    
    fn user_message(&self) -> String {
        self.error.user_message()
    }
}

/// 重试配置 - 简化版本
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_attempts: usize,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_factor: f64,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(5),
            backoff_factor: 2.0,
        }
    }
}

/// 重试执行器 - 简化版本
pub struct RetryExecutor {
    config: RetryConfig,
}

impl RetryExecutor {
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }
    
    /// 同步重试
    pub fn retry<T, F>(&self, mut operation: F) -> CoreResult<T>
    where
        F: FnMut() -> CoreResult<T>,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.config.max_attempts {
            match operation() {
                Ok(result) => return Ok(result),
                Err(error) => {
                    if !error.is_retryable() || attempt == self.config.max_attempts {
                        return Err(error);
                    }
                    
                    let delay = self.calculate_delay(attempt);
                    std::thread::sleep(delay);
                    last_error = Some(error);
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    /// 异步重试
    pub async fn async_retry<T, F, Fut>(&self, mut operation: F) -> CoreResult<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = CoreResult<T>>,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    if !error.is_retryable() || attempt == self.config.max_attempts {
                        return Err(error);
                    }
                    
                    let delay = self.calculate_delay(attempt);
                    tokio::time::sleep(delay).await;
                    last_error = Some(error);
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    fn calculate_delay(&self, attempt: usize) -> Duration {
        let delay = self.config.base_delay.as_millis() as f64 
            * self.config.backoff_factor.powi((attempt - 1) as i32);
        
        Duration::from_millis(delay.min(self.config.max_delay.as_millis() as f64) as u64)
    }
}

/// 错误收集器 - 用于批量操作
#[derive(Debug, Default)]
pub struct ErrorCollector {
    errors: Vec<SimpleError>,
    context: String,
}

impl ErrorCollector {
    pub fn new(context: impl Into<String>) -> Self {
        Self {
            errors: Vec::new(),
            context: context.into(),
        }
    }
    
    pub fn add_error(&mut self, error: SimpleError) {
        self.errors.push(error);
    }
    
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }
    
    pub fn error_count(&self) -> usize {
        self.errors.len()
    }
    
    pub fn into_result<T>(self, success_value: T) -> CoreResult<T> {
        if self.errors.is_empty() {
            Ok(success_value)
        } else {
            let message = format!(
                "{}: {} errors occurred", 
                self.context, 
                self.errors.len()
            );
            
            Err(SimpleError::technical("BATCH_ERROR", message))
        }
    }
    
    pub fn errors(&self) -> &[SimpleError] {
        &self.errors
    }
}

/// 扩展的 Result 方法
pub trait ExtendedResultExt<T> {
    /// 添加详细上下文
    fn with_detailed_context(self, context: ErrorContext) -> Result<T, ContextualError>;
    
    /// 记录错误
    fn log_error(self, level: tracing::Level) -> CoreResult<T>;
    
    /// 转换为可选值，记录错误
    fn ok_or_log(self) -> Option<T>;
}

impl<T> ExtendedResultExt<T> for CoreResult<T> {
    fn with_detailed_context(self, context: ErrorContext) -> Result<T, ContextualError> {
        self.map_err(|error| ContextualError::new(error, context))
    }
    
    fn log_error(self, level: tracing::Level) -> CoreResult<T> {
        self.map_err(|error| {
            match level {
                tracing::Level::ERROR => tracing::error!("{}", error),
                tracing::Level::WARN => tracing::warn!("{}", error),
                tracing::Level::INFO => tracing::info!("{}", error),
                tracing::Level::DEBUG => tracing::debug!("{}", error),
                tracing::Level::TRACE => tracing::trace!("{}", error),
            }
            error
        })
    }
    
    fn ok_or_log(self) -> Option<T> {
        match self {
            Ok(value) => Some(value),
            Err(error) => {
                tracing::error!("{}", error);
                None
            }
        }
    }
}

/// 便利宏 - 创建带上下文的错误
#[macro_export]
macro_rules! contextual_error {
    ($error:expr, $($key:ident = $value:expr),*) => {
        {
            let mut context = $crate::errors::extensions::ErrorContext::new();
            $(
                context = context.$key($value);
            )*
            $crate::errors::extensions::ContextualError::new($error, context)
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::simple_error;
    
    #[test]
    fn test_error_context() {
        let context = ErrorContext::new()
            .with_module("test_module")
            .with_function("test_function")
            .with_user_id(uuid::Uuid::new_v4());
        
        assert_eq!(context.module, Some("test_module"));
        assert_eq!(context.function, Some("test_function"));
        assert!(context.user_id.is_some());
    }
    
    #[test]
    fn test_contextual_error() {
        let simple_error = simple_error!(business, "TEST_001", "Test error");
        let context = ErrorContext::new().with_module("test");
        let contextual = ContextualError::new(simple_error, context);
        
        assert_eq!(contextual.error_code(), "TEST_001");
        assert_eq!(contextual.context.module, Some("test"));
    }
    
    #[test]
    fn test_error_collector() {
        let mut collector = ErrorCollector::new("test batch");
        
        collector.add_error(simple_error!(validation, "VAL_001", "Error 1"));
        collector.add_error(simple_error!(validation, "VAL_002", "Error 2"));
        
        assert_eq!(collector.error_count(), 2);
        assert!(collector.has_errors());
        
        let result = collector.into_result("success");
        assert!(result.is_err());
    }
    
    #[tokio::test]
    async fn test_retry_executor() {
        let config = RetryConfig::default();
        let executor = RetryExecutor::new(config);
        
        let mut attempt_count = 0;
        let result = executor.async_retry(|| {
            attempt_count += 1;
            async move {
                if attempt_count < 3 {
                    Err(simple_error!(external, "NET_001", "Network error"))
                } else {
                    Ok("success")
                }
            }
        }).await;
        
        assert!(result.is_ok());
        assert_eq!(attempt_count, 3);
    }
}
