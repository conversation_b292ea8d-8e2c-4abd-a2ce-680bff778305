//! 数据库管理系统

use sigmax_core::{SigmaXResult, SigmaXError, DatabaseConfig};
use sqlx::{PgPool, Row, Transaction, Postgres};
use std::time::{Duration, Instant};
use std::sync::Arc;
use crate::performance::{PerformanceMonitor, PerformanceConfig, PerformanceStats};

/// 数据库事务包装器
///
/// 提供类型安全的事务操作，确保事务的正确提交或回滚
pub struct DatabaseTransaction<'a> {
    transaction: Option<Transaction<'a, Postgres>>,
    is_committed: bool,
}

impl<'a> DatabaseTransaction<'a> {
    /// 创建新的数据库事务
    pub fn new(transaction: Transaction<'a, Postgres>) -> Self {
        Self {
            transaction: Some(transaction),
            is_committed: false,
        }
    }

    /// 获取事务的可变引用（用于执行查询）
    pub fn as_mut(&mut self) -> Option<&mut Transaction<'a, Postgres>> {
        self.transaction.as_mut()
    }

    /// 提交事务
    pub async fn commit(mut self) -> SigmaXResult<()> {
        if let Some(tx) = self.transaction.take() {
            tx.commit().await
                .map_err(|e| SigmaXError::database_error(format!("提交事务失败: {}", e)))?;
            self.is_committed = true;
        }
        Ok(())
    }

    /// 回滚事务
    pub async fn rollback(mut self) -> SigmaXResult<()> {
        if let Some(tx) = self.transaction.take() {
            tx.rollback().await
                .map_err(|e| SigmaXError::database_error(format!("回滚事务失败: {}", e)))?;
        }
        Ok(())
    }

    /// 检查事务是否已提交
    pub fn is_committed(&self) -> bool {
        self.is_committed
    }
}

impl<'a> Drop for DatabaseTransaction<'a> {
    fn drop(&mut self) {
        if self.transaction.is_some() && !self.is_committed {
            // 事务在 drop 时会自动回滚
            tracing::warn!("数据库事务在未提交的情况下被丢弃，将自动回滚");
        }
    }
}

/// 事务统计信息
#[derive(Debug, Clone)]
pub struct TransactionStats {
    /// 活跃连接数
    pub active_connections: u32,
    /// 事务中空闲连接数
    pub idle_in_transaction: u32,
    /// 空闲连接数
    pub idle_connections: u32,
}

/// 数据库管理器
#[derive(Debug)]
pub struct DatabaseManager {
    pool: PgPool,
    config: DatabaseConfig,
    performance_monitor: Option<Arc<crate::performance::PerformanceMonitor>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(config: DatabaseConfig) -> SigmaXResult<Self> {
        let pool = Self::create_connection_pool(&config).await?;

        let manager = Self {
            pool,
            config,
            performance_monitor: None,
        };

        // 如果启用自动迁移，执行迁移
        if manager.config.auto_migrate {
            manager.run_migrations().await?;
        }

        Ok(manager)
    }

    /// 创建模拟数据库管理器（用于测试）
    pub fn new_mock() -> SigmaXResult<Self> {
        // 创建一个空的PostgreSQL连接池（不实际连接）
        let config = DatabaseConfig {
            url: "postgresql://mock".to_string(),
            max_connections: 1,
            connection_timeout: 30,
            auto_migrate: false,
        };

        // 创建一个假的连接池
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(1)
            .connect_lazy(&config.url)
            .map_err(|e| SigmaXError::database_error(format!("Failed to create mock database: {}", e)))?;

        Ok(Self {
            pool,
            config,
            performance_monitor: None,
        })
    }
    
    /// 创建连接池
    async fn create_connection_pool(config: &DatabaseConfig) -> SigmaXResult<PgPool> {
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(config.max_connections)
            .acquire_timeout(Duration::from_secs(config.connection_timeout))
            .connect(&config.url)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to connect to database: {}", e)))?;
        
        Ok(pool)
    }
    
    /// 获取数据库连接池
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }
    
    /// 检查数据库连接
    pub async fn health_check(&self) -> SigmaXResult<()> {
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Database health check failed: {}", e)))?;
        
        Ok(())
    }
    
    /// 运行数据库迁移
    pub async fn run_migrations(&self) -> SigmaXResult<()> {
        tracing::info!("Starting database migrations...");

        // 检查是否需要执行迁移
        if !self.should_run_migrations().await? {
            tracing::info!("Database schema is up to date, skipping migrations");
            return Ok(());
        }

        // 执行外部 SQL 脚本进行迁移
        self.execute_external_migrations().await?;

        tracing::info!("Database migrations completed successfully");
        Ok(())
    }

    /// 检查是否需要运行迁移
    async fn should_run_migrations(&self) -> SigmaXResult<bool> {
        // 检查关键表是否存在
        let result = sqlx::query(
            r#"
            SELECT COUNT(*) as table_count
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('exchanges', 'trading_pairs', 'orders', 'trades', 'strategies', 'portfolios')
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("Failed to check existing tables: {}", e)))?;

        let table_count: i64 = result.get("table_count");

        // 如果核心表不完整，需要运行迁移
        Ok(table_count < 6)
    }

    /// 执行外部迁移脚本
    async fn execute_external_migrations(&self) -> SigmaXResult<()> {
        tracing::warn!("External SQL migration script execution is not implemented in Rust code");
        tracing::warn!("Please run the following command manually to initialize the database:");
        tracing::warn!("cd database/external-db && ./setup.sh \"{}\"", self.config.url);
        tracing::warn!("Or execute: psql \"{}\" -f database/external-db/init_database.sql", self.config.url);

        // 对于自动迁移，我们只执行基本的表创建
        // 完整的迁移应该通过外部脚本执行
        self.create_tables().await?;

        Ok(())
    }
    
    /// 创建数据库表
    /// 注意：表创建逻辑已迁移到外部SQL脚本 (database/external-db/init_database.sql)
    /// 使用 setup.sh 脚本或直接执行 SQL 文件来初始化数据库结构
    async fn create_tables(&self) -> SigmaXResult<()> {
        // 表创建逻辑已迁移到 database/external-db/init_database.sql
        // 请使用以下方式初始化数据库：
        // 1. 运行 database/external-db/setup.sh 脚本
        // 2. 或直接执行: psql $DATABASE_URL -f database/external-db/init_database.sql

        tracing::info!("Table creation logic has been migrated to external SQL scripts");
        tracing::info!("Please use database/external-db/setup.sh to initialize database schema");

        Ok(())
    }
    
    /// 创建数据库索引
    /// 注意：索引创建逻辑已迁移到外部SQL脚本 (database/external-db/init_database.sql)
    #[allow(dead_code)]
    async fn create_indexes(&self) -> SigmaXResult<()> {
        tracing::info!("Index creation logic has been migrated to external SQL scripts");
        Ok(())
    }
    
    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> SigmaXResult<DatabaseStats> {
        let row = sqlx::query(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM trading_pairs) as trading_pairs_count,
                (SELECT COUNT(*) FROM candles) as candles_count,
                (SELECT COUNT(*) FROM orders) as orders_count,
                (SELECT COUNT(*) FROM trades) as trades_count,
                (SELECT COUNT(*) FROM strategies) as strategies_count,
                (SELECT COUNT(*) FROM portfolios) as portfolios_count,
                (SELECT COUNT(*) FROM positions) as positions_count
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("Failed to get database stats: {}", e)))?;
        
        Ok(DatabaseStats {
            trading_pairs_count: row.get::<i64, _>("trading_pairs_count") as u64,
            candles_count: row.get::<i64, _>("candles_count") as u64,
            orders_count: row.get::<i64, _>("orders_count") as u64,
            trades_count: row.get::<i64, _>("trades_count") as u64,
            strategies_count: row.get::<i64, _>("strategies_count") as u64,
            portfolios_count: row.get::<i64, _>("portfolios_count") as u64,
            positions_count: row.get::<i64, _>("positions_count") as u64,
        })
    }
    
    /// 清理旧数据
    pub async fn cleanup_old_data(&self, days: u32) -> SigmaXResult<u64> {
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(days as i64);
        
        let result = sqlx::query(
            "DELETE FROM candles WHERE created_at < $1"
        )
        .bind(cutoff_date)
        .execute(&self.pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("Failed to cleanup old data: {}", e)))?;

        Ok(result.rows_affected())
    }

    // ============================================================================
    // 事务管理
    // ============================================================================

    /// 开始一个新事务
    pub async fn begin_transaction(&self) -> SigmaXResult<DatabaseTransaction> {
        let tx = self.pool.begin().await
            .map_err(|e| SigmaXError::database_error(format!("开始事务失败: {}", e)))?;

        Ok(DatabaseTransaction::new(tx))
    }

    /// 在事务中执行多个操作
    ///
    /// 使用示例：
    /// ```rust
    /// let result = db_manager.run_in_transaction(|tx| async move {
    ///     // 在这里执行多个数据库操作
    ///     sqlx::query("INSERT INTO ...").execute(tx).await?;
    ///     sqlx::query("UPDATE ...").execute(tx).await?;
    ///     Ok(some_result)
    /// }).await?;
    /// ```
    pub async fn run_in_transaction<F, Fut, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<R>>,
    {
        let mut tx = self.pool.begin().await
            .map_err(|e| SigmaXError::database_error(format!("开始事务失败: {}", e)))?;

        match operation(&mut tx).await {
            Ok(result) => {
                tx.commit().await
                    .map_err(|e| SigmaXError::database_error(format!("提交事务失败: {}", e)))?;
                Ok(result)
            }
            Err(e) => {
                if let Err(rollback_err) = tx.rollback().await {
                    tracing::error!("回滚事务失败: {}", rollback_err);
                }
                Err(e)
            }
        }
    }

    /// 获取事务统计信息
    pub async fn get_transaction_stats(&self) -> SigmaXResult<TransactionStats> {
        let pool = self.pool();

        let row = sqlx::query(
            r#"
            SELECT
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle in transaction') as idle_in_transaction,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections
            "#
        )
        .fetch_one(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("获取事务统计失败: {}", e)))?;

        Ok(TransactionStats {
            active_connections: row.get::<i64, _>("active_connections") as u32,
            idle_in_transaction: row.get::<i64, _>("idle_in_transaction") as u32,
            idle_connections: row.get::<i64, _>("idle_connections") as u32,
        })
    }
}

/// 数据库统计信息
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub trading_pairs_count: u64,
    pub candles_count: u64,
    pub orders_count: u64,
    pub trades_count: u64,
    pub strategies_count: u64,
    pub portfolios_count: u64,
    pub positions_count: u64,
}

/// 事务执行器
///
/// 提供便利的事务执行方法
pub struct TransactionExecutor<'a> {
    db_manager: &'a DatabaseManager,
}

impl<'a> TransactionExecutor<'a> {
    pub fn new(db_manager: &'a DatabaseManager) -> Self {
        Self { db_manager }
    }

    /// 执行批量操作（简化版本）
    pub async fn execute<F, Fut, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<R>>,
    {
        self.db_manager.run_in_transaction(operation).await
    }
}

impl DatabaseManager {
    /// 获取事务执行器
    pub fn transaction_executor(&self) -> TransactionExecutor<'_> {
        TransactionExecutor::new(self)
    }

    /// 检查是否有长时间运行的事务
    pub async fn check_long_running_transactions(&self, threshold_seconds: u32) -> SigmaXResult<Vec<LongRunningTransaction>> {
        let pool = self.pool();

        let rows = sqlx::query(
            r#"
            SELECT
                pid,
                usename,
                application_name,
                state,
                query_start,
                state_change,
                query,
                EXTRACT(EPOCH FROM (NOW() - query_start)) as duration_seconds
            FROM pg_stat_activity
            WHERE state IN ('active', 'idle in transaction')
            AND query_start IS NOT NULL
            AND EXTRACT(EPOCH FROM (NOW() - query_start)) > $1
            ORDER BY query_start ASC
            "#
        )
        .bind(threshold_seconds as i32)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("检查长时间运行事务失败: {}", e)))?;

        let mut transactions = Vec::new();
        for row in rows {
            transactions.push(LongRunningTransaction {
                pid: row.get::<i32, _>("pid") as u32,
                username: row.get("usename"),
                application_name: row.get("application_name"),
                state: row.get("state"),
                query: row.get("query"),
                duration_seconds: row.get::<f64, _>("duration_seconds") as u32,
            });
        }

        Ok(transactions)
    }

    // ============================================================================
    // 性能监控
    // ============================================================================

    /// 启用性能监控
    pub fn enable_performance_monitoring(&mut self, config: crate::performance::PerformanceConfig) {
        let monitor = crate::performance::PerformanceMonitor::new(
            Arc::new(self.pool.clone()),
            config
        );
        self.performance_monitor = Some(Arc::new(monitor));
    }

    /// 获取性能监控器
    pub fn performance_monitor(&self) -> Option<&Arc<crate::performance::PerformanceMonitor>> {
        self.performance_monitor.as_ref()
    }

    /// 执行带性能监控的查询
    pub async fn execute_with_monitoring<F, R>(&self, query_name: &str, operation: F) -> SigmaXResult<R>
    where
        F: std::future::Future<Output = SigmaXResult<R>>,
    {
        let start_time = Instant::now();
        let result = operation.await;
        let duration = start_time.elapsed();

        // 记录性能数据
        if let Some(monitor) = &self.performance_monitor {
            let error = if result.is_err() {
                Some("Query execution failed")
            } else {
                None
            };

            let _ = monitor.record_query_execution(query_name, duration, error, 0);
        }

        result
    }

    /// 获取性能统计报告
    pub async fn get_performance_stats(&self) -> SigmaXResult<Option<crate::performance::PerformanceStats>> {
        if let Some(monitor) = &self.performance_monitor {
            Ok(Some(monitor.generate_stats().await?))
        } else {
            Ok(None)
        }
    }

    /// 清理性能监控数据
    pub async fn cleanup_performance_data(&self) -> SigmaXResult<()> {
        if let Some(monitor) = &self.performance_monitor {
            monitor.cleanup_expired_data().await?;
        }
        Ok(())
    }

    /// 获取慢查询报告
    pub fn get_slow_queries(&self, limit: Option<usize>) -> Vec<crate::performance::SlowQuery> {
        if let Some(monitor) = &self.performance_monitor {
            monitor.get_slow_queries(limit)
        } else {
            Vec::new()
        }
    }

    /// 获取查询性能指标
    pub fn get_query_metrics(&self) -> Vec<crate::performance::QueryMetrics> {
        if let Some(monitor) = &self.performance_monitor {
            monitor.get_query_metrics()
        } else {
            Vec::new()
        }
    }
}

/// 长时间运行的事务信息
#[derive(Debug, Clone)]
pub struct LongRunningTransaction {
    pub pid: u32,
    pub username: String,
    pub application_name: Option<String>,
    pub state: String,
    pub query: Option<String>,
    pub duration_seconds: u32,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_database_config_validation() {
        let config = DatabaseConfig {
            url: "postgresql://localhost/test".to_string(),
            max_connections: 5,
            connection_timeout: 30,
            auto_migrate: false,
        };
        
        // 这个测试只验证配置结构，不实际连接数据库
        assert_eq!(config.max_connections, 5);
        assert_eq!(config.connection_timeout, 30);
        assert!(!config.auto_migrate);
    }
    
    #[test]
    fn test_database_stats() {
        let stats = DatabaseStats {
            trading_pairs_count: 10,
            candles_count: 1000,
            orders_count: 50,
            trades_count: 25,
            strategies_count: 3,
            portfolios_count: 1,
            positions_count: 5,
        };
        
        assert_eq!(stats.trading_pairs_count, 10);
        assert_eq!(stats.candles_count, 1000);
    }
}
