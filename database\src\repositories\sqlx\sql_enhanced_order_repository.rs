//! SQL 增强订单仓储实现
//!
//! 提供完整的订单数据访问功能，包括基础CRUD、业务查询和聚合操作

use std::sync::Arc;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sqlx::{Row, QueryBuilder, Postgres};
use rust_decimal::prelude::*;
use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, TradingPair, ExchangeId,
    OrderSide, OrderType, Quantity, Price, Amount, SigmaXResult, SigmaXError
};

use crate::DatabaseManager;
use crate::repositories::traits::enhanced_order_repository::{
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort,
    OrderSortBy, SortDirection, OrderStatistics
};

/// SQL 增强订单仓储实现
pub struct SqlEnhancedOrderRepository {
    db: Arc<DatabaseManager>,
}

impl SqlEnhancedOrderRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 构建基础查询
    fn build_base_query() -> String {
        r#"
        SELECT o.id, o.strategy_id, o.exchange_id, o.trading_pair_base, o.trading_pair_quote,
               o.side, o.order_type, o.quantity, o.price, o.stop_price, o.status,
               o.filled_quantity, o.average_price, o.created_at, o.updated_at
        FROM orders o
        "#.to_string()
    }

    /// 应用查询过滤器
    fn apply_filter<'a>(
        query_builder: &mut QueryBuilder<'a, Postgres>,
        filter: &'a OrderQueryFilter,
        has_where: &mut bool,
    ) {
        if let Some(strategy_id) = &filter.strategy_id {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.strategy_id = ");
            query_builder.push_bind(strategy_id);
        }

        if let Some(exchange_id) = &filter.exchange_id {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.exchange_id = ");
            query_builder.push_bind(exchange_id.to_string());
        }

        if let Some(trading_pair) = &filter.trading_pair {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" tp.symbol = ");
            query_builder.push_bind(trading_pair.symbol());
        }

        if let Some(status) = &filter.status {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.status = ");
            query_builder.push_bind(format!("{:?}", status));
        }

        if let Some(side) = &filter.side {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.side = ");
            query_builder.push_bind(format!("{:?}", side));
        }

        if let Some(order_type) = &filter.order_type {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.order_type = ");
            query_builder.push_bind(format!("{:?}", order_type));
        }

        if let Some(created_after) = &filter.created_after {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.created_at >= ");
            query_builder.push_bind(created_after);
        }

        if let Some(created_before) = &filter.created_before {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.created_at <= ");
            query_builder.push_bind(created_before);
        }

        if let Some(min_quantity) = &filter.min_quantity {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.quantity >= ");
            query_builder.push_bind(min_quantity);
        }

        if let Some(max_quantity) = &filter.max_quantity {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.quantity <= ");
            query_builder.push_bind(max_quantity);
        }

        if let Some(min_price) = &filter.min_price {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.price >= ");
            query_builder.push_bind(min_price);
        }

        if let Some(max_price) = &filter.max_price {
            Self::add_where_clause(query_builder, has_where);
            query_builder.push(" o.price <= ");
            query_builder.push_bind(max_price);
        }
    }

    /// 添加 WHERE 子句
    fn add_where_clause(query_builder: &mut QueryBuilder<Postgres>, has_where: &mut bool) {
        if *has_where {
            query_builder.push(" AND");
        } else {
            query_builder.push(" WHERE");
            *has_where = true;
        }
    }

    /// 应用排序
    fn apply_sort(query_builder: &mut QueryBuilder<Postgres>, sort: &OrderSort) {
        query_builder.push(" ORDER BY ");
        
        match sort.sort_by {
            OrderSortBy::CreatedAt => query_builder.push("o.created_at"),
            OrderSortBy::UpdatedAt => query_builder.push("o.updated_at"),
            OrderSortBy::Quantity => query_builder.push("o.quantity"),
            OrderSortBy::Price => query_builder.push("o.price"),
            OrderSortBy::Status => query_builder.push("o.status"),
        };

        match sort.direction {
            SortDirection::Asc => query_builder.push(" ASC"),
            SortDirection::Desc => query_builder.push(" DESC"),
        };
    }

    /// 应用分页
    fn apply_pagination(query_builder: &mut QueryBuilder<Postgres>, pagination: &Pagination) {
        query_builder.push(" LIMIT ");
        query_builder.push_bind(pagination.limit as i64);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(pagination.offset as i64);
    }

    /// 将数据库行转换为订单对象
    fn row_to_order(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<Order> {
        let order_type_str: String = row.get("order_type");
        let order_type = match order_type_str.as_str() {
            "Market" => OrderType::Market,
            "Limit" => OrderType::Limit,
            "StopLoss" => OrderType::StopLoss,
            "StopLimit" => OrderType::StopLimit,
            _ => return Err(SigmaXError::database_error(format!("Unknown order type: {}", order_type_str))),
        };

        let side_str: String = row.get("side");
        let side = match side_str.as_str() {
            "Buy" => OrderSide::Buy,
            "Sell" => OrderSide::Sell,
            _ => return Err(SigmaXError::database_error(format!("Unknown order side: {}", side_str))),
        };

        let status_str: String = row.get("status");
        let status = match status_str.as_str() {
            "Pending" => OrderStatus::Pending,
            "PartiallyFilled" => OrderStatus::PartiallyFilled,
            "Filled" => OrderStatus::Filled,
            "Cancelled" => OrderStatus::Cancelled,
            "Rejected" => OrderStatus::Rejected,
            _ => return Err(SigmaXError::database_error(format!("Unknown order status: {}", status_str))),
        };

        let trading_pair_base: String = row.get("trading_pair_base");
        let trading_pair_quote: String = row.get("trading_pair_quote");
        let trading_pair = TradingPair::new(trading_pair_base, trading_pair_quote);

        let exchange_id_str: String = row.get("exchange_id");
        let exchange_id = ExchangeId::from(exchange_id_str);

        Ok(Order {
            id: row.get("id"),
            strategy_id: row.get("strategy_id"),
            exchange_id,
            trading_pair,
            side,
            order_type,
            quantity: row.get("quantity"),
            price: row.get("price"),
            stop_price: row.get("stop_price"),
            status,
            filled_quantity: row.get("filled_quantity"),
            average_price: row.get("average_price"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }
}

#[async_trait]
impl EnhancedOrderRepository for SqlEnhancedOrderRepository {
    // ============================================================================
    // 基础 CRUD 操作
    // ============================================================================
    
    async fn save(&self, order: &Order) -> SigmaXResult<()> {
        // 验证订单
        order.validate_complete()
            .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;

        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO orders (
                id, strategy_id, exchange_id, trading_pair_base, trading_pair_quote,
                side, order_type, quantity, price, stop_price, status,
                filled_quantity, average_price, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
            )
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                filled_quantity = EXCLUDED.filled_quantity,
                average_price = EXCLUDED.average_price,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&order.id)
        .bind(&order.strategy_id)
        .bind(&format!("{:?}", order.exchange_id))
        .bind(&order.trading_pair.base)
        .bind(&order.trading_pair.quote)
        .bind(&format!("{:?}", order.side))
        .bind(&format!("{:?}", order.order_type))
        .bind(&order.quantity)
        .bind(&order.price)
        .bind(&order.stop_price)
        .bind(&format!("{:?}", order.status))
        .bind(&order.filled_quantity)
        .bind(&order.average_price)
        .bind(&order.created_at)
        .bind(&order.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("保存订单失败: {}", e)))?;

        Ok(())
    }

    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>> {
        let pool = self.db.pool();

        let row = sqlx::query(&format!("{} WHERE o.id = $1", Self::build_base_query()))
            .bind(&id)
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("查找订单失败: {}", e)))?;

        if let Some(row) = row {
            Ok(Some(self.row_to_order(row)?))
        } else {
            Ok(None)
        }
    }

    async fn update(&self, order: &Order) -> SigmaXResult<()> {
        // 验证订单
        order.validate_complete()
            .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;

        let pool = self.db.pool();

        let result = sqlx::query(
            r#"
            UPDATE orders SET
                status = $2,
                filled_quantity = $3,
                average_price = $4,
                updated_at = $5
            WHERE id = $1
            "#
        )
        .bind(&order.id)
        .bind(&format!("{:?}", order.status))
        .bind(&order.filled_quantity)
        .bind(&order.average_price)
        .bind(&order.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("更新订单失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database_error("订单不存在".to_string()));
        }

        Ok(())
    }

    async fn delete(&self, id: OrderId) -> SigmaXResult<()> {
        let pool = self.db.pool();

        let result = sqlx::query("DELETE FROM orders WHERE id = $1")
            .bind(&id)
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("删除订单失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database_error("订单不存在".to_string()));
        }

        Ok(())
    }

    async fn exists(&self, id: OrderId) -> SigmaXResult<bool> {
        let pool = self.db.pool();

        let row = sqlx::query("SELECT COUNT(*) as count FROM orders WHERE id = $1")
            .bind(&id)
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("检查订单存在性失败: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count > 0)
    }

    // ============================================================================
    // 批量操作
    // ============================================================================

    async fn save_batch(&self, orders: &[Order]) -> SigmaXResult<()> {
        if orders.is_empty() {
            return Ok(());
        }

        // 验证所有订单
        for order in orders {
            order.validate_complete()
                .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::database_error(format!("开始事务失败: {}", e)))?;

        for order in orders {
            sqlx::query(
                r#"
                INSERT INTO orders (
                    id, strategy_id, exchange_id, trading_pair_base, trading_pair_quote,
                    side, order_type, quantity, price, stop_price, status,
                    filled_quantity, average_price, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
                )
                ON CONFLICT (id) DO UPDATE SET
                    status = EXCLUDED.status,
                    filled_quantity = EXCLUDED.filled_quantity,
                    average_price = EXCLUDED.average_price,
                    updated_at = EXCLUDED.updated_at
                "#
            )
            .bind(&order.id)
            .bind(&order.strategy_id)
            .bind(&format!("{:?}", order.exchange_id))
            .bind(&order.trading_pair.base)
            .bind(&order.trading_pair.quote)
            .bind(&format!("{:?}", order.side))
            .bind(&format!("{:?}", order.order_type))
            .bind(&order.quantity)
            .bind(&order.price)
            .bind(&order.stop_price)
            .bind(&format!("{:?}", order.status))
            .bind(&order.filled_quantity)
            .bind(&order.average_price)
            .bind(&order.created_at)
            .bind(&order.updated_at)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("批量保存订单失败: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::database_error(format!("提交事务失败: {}", e)))?;

        Ok(())
    }

    async fn update_status_batch(&self, ids: &[OrderId], status: OrderStatus) -> SigmaXResult<()> {
        if ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = QueryBuilder::new("UPDATE orders SET status = ");
        query_builder.push_bind(format!("{:?}", status));
        query_builder.push(", updated_at = NOW() WHERE id = ANY(");
        query_builder.push_bind(ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("批量更新订单状态失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database_error("没有订单被更新".to_string()));
        }

        Ok(())
    }

    async fn delete_batch(&self, ids: &[OrderId]) -> SigmaXResult<()> {
        if ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = QueryBuilder::new("DELETE FROM orders WHERE id = ANY(");
        query_builder.push_bind(ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("批量删除订单失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database_error("没有订单被删除".to_string()));
        }

        Ok(())
    }

    // ============================================================================
    // 条件查询
    // ============================================================================

    async fn find_by_filter(
        &self,
        filter: &OrderQueryFilter,
        pagination: Option<&Pagination>,
        sort: Option<&OrderSort>
    ) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();
        let mut query_builder = QueryBuilder::new(&Self::build_base_query());
        let mut has_where = false;

        // 应用过滤器
        Self::apply_filter(&mut query_builder, filter, &mut has_where);

        // 应用排序
        if let Some(sort) = sort {
            Self::apply_sort(&mut query_builder, sort);
        } else {
            query_builder.push(" ORDER BY o.created_at DESC");
        }

        // 应用分页
        if let Some(pagination) = pagination {
            Self::apply_pagination(&mut query_builder, pagination);
        }

        let rows = query_builder.build()
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("条件查询订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            status: Some(status),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            strategy_id: Some(strategy_id),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            trading_pair: Some(trading_pair.clone()),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_exchange(&self, exchange_id: &ExchangeId) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            exchange_id: Some(exchange_id.clone()),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_pending_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.find_by_status(OrderStatus::Pending).await
    }

    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            "{} WHERE o.status IN ('Pending', 'PartiallyFilled') ORDER BY o.created_at DESC",
            Self::build_base_query()
        ))
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("查找活跃订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn find_by_timerange(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>
    ) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            created_after: Some(start),
            created_before: Some(end),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    // ============================================================================
    // 聚合查询
    // ============================================================================

    async fn count_by_status(&self, status: OrderStatus) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let row = sqlx::query("SELECT COUNT(*) as count FROM orders WHERE status = $1")
            .bind(format!("{:?}", status))
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("统计订单数量失败: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count as u64)
    }

    async fn count_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let row = sqlx::query("SELECT COUNT(*) as count FROM orders WHERE strategy_id = $1")
            .bind(&strategy_id)
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("统计策略订单数量失败: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count as u64)
    }

    async fn get_total_volume_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Quantity> {
        let pool = self.db.pool();

        let row = sqlx::query("SELECT COALESCE(SUM(filled_quantity), 0) as total_volume FROM orders WHERE strategy_id = $1")
            .bind(&strategy_id)
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("计算策略总交易量失败: {}", e)))?;

        let total_volume: Quantity = row.get("total_volume");
        Ok(total_volume)
    }

    async fn get_total_value_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Amount> {
        let pool = self.db.pool();

        let row = sqlx::query(
            "SELECT COALESCE(SUM(filled_quantity * COALESCE(average_price, price)), 0) as total_value FROM orders WHERE strategy_id = $1"
        )
        .bind(&strategy_id)
        .fetch_one(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("计算策略总交易价值失败: {}", e)))?;

        let total_value: Amount = row.get("total_value");
        Ok(total_value)
    }

    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics> {
        let pool = self.db.pool();

        let mut query_builder = QueryBuilder::new(
            r#"
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN status = 'Filled' THEN 1 END) as filled_orders,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_orders,
                COUNT(CASE WHEN status = 'PartiallyFilled' THEN 1 END) as partially_filled_orders,
                COALESCE(SUM(quantity), 0) as total_quantity,
                COALESCE(SUM(filled_quantity), 0) as total_filled_quantity,
                COALESCE(SUM(filled_quantity * COALESCE(average_price, price)), 0) as total_value,
                COALESCE(AVG(price), 0) as average_price,
                COALESCE(MIN(price), 0) as min_price,
                COALESCE(MAX(price), 0) as max_price
            FROM orders o
            "#
        );

        let mut has_where = false;
        if let Some(filter) = filter {
            query_builder.push(" JOIN trading_pairs tp ON o.trading_pair_id = tp.id");
            Self::apply_filter(&mut query_builder, filter, &mut has_where);
        }

        let row = query_builder.build()
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("获取订单统计失败: {}", e)))?;

        let total_orders = row.get::<i64, _>("total_orders") as u64;
        let total_quantity: Quantity = row.get("total_quantity");
        let average_order_size = if total_orders > 0 {
            total_quantity / Quantity::from(total_orders)
        } else {
            Quantity::ZERO
        };

        Ok(OrderStatistics {
            total_count: total_orders,
            pending_count: row.get::<i64, _>("pending_orders") as u64,
            filled_count: row.get::<i64, _>("filled_orders") as u64,
            cancelled_count: row.get::<i64, _>("cancelled_orders") as u64,
            total_volume: total_quantity,
            total_value: row.get("total_value"),
            average_order_size,
        })
    }

    async fn get_strategy_statistics(&self, strategy_id: StrategyId) -> SigmaXResult<OrderStatistics> {
        let filter = OrderQueryFilter {
            strategy_id: Some(strategy_id),
            ..Default::default()
        };
        self.get_statistics(Some(&filter)).await
    }

    // ============================================================================
    // 业务查询
    // ============================================================================

    async fn find_orders_for_risk_check(&self) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            "{} WHERE o.status IN ('Pending', 'PartiallyFilled') AND o.created_at > NOW() - INTERVAL '1 hour' ORDER BY o.created_at DESC",
            Self::build_base_query()
        ))
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("查找需要风险检查的订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn find_timeout_pending_orders(&self, timeout_minutes: u32) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            "{} WHERE o.status = 'Pending' AND o.created_at < NOW() - INTERVAL '{} minutes' ORDER BY o.created_at ASC",
            Self::build_base_query(), timeout_minutes
        ))
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("查找超时待处理订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn find_large_orders(&self, min_value: Amount) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            "{} WHERE (o.quantity * o.price) >= $1 ORDER BY (o.quantity * o.price) DESC",
            Self::build_base_query()
        ))
        .bind(&min_value)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("查找大额订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn find_abnormal_orders(
        &self,
        trading_pair: &TradingPair,
        market_price: Price,
        deviation_percent: f64
    ) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();
        let deviation_factor = Quantity::from_f64(1.0 - deviation_percent / 100.0).unwrap_or(Quantity::ONE);
        let upper_factor = Quantity::from_f64(1.0 + deviation_percent / 100.0).unwrap_or(Quantity::ONE);
        let lower_bound = market_price * deviation_factor;
        let upper_bound = market_price * upper_factor;

        let rows = sqlx::query(&format!(
            "{} WHERE tp.symbol = $1 AND o.status IN ('Pending', 'PartiallyFilled') AND (o.price < $2 OR o.price > $3) ORDER BY ABS(o.price - $4) DESC",
            Self::build_base_query()
        ))
        .bind(&trading_pair.symbol())
        .bind(&lower_bound)
        .bind(&upper_bound)
        .bind(&market_price)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("查找异常订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    // ============================================================================
    // 性能优化查询
    // ============================================================================

    async fn get_recent_orders(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            "{} ORDER BY o.created_at DESC LIMIT $1",
            Self::build_base_query()
        ))
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("获取最近订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn get_orders_by_popular_pairs(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(&format!(
            r#"
            {}
            WHERE tp.symbol IN (
                SELECT tp2.symbol
                FROM orders o2
                JOIN trading_pairs tp2 ON o2.trading_pair_id = tp2.id
                WHERE o2.created_at > NOW() - INTERVAL '24 hours'
                GROUP BY tp2.symbol
                ORDER BY COUNT(*) DESC
                LIMIT 10
            )
            ORDER BY o.created_at DESC
            LIMIT $1
            "#,
            Self::build_base_query()
        ))
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database_error(format!("获取热门交易对订单失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn preload_orders_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>> {
        if order_ids.is_empty() {
            return Ok(Vec::new());
        }

        let pool = self.db.pool();
        let mut query_builder = QueryBuilder::new(&Self::build_base_query());
        query_builder.push(" WHERE o.id = ANY(");
        query_builder.push_bind(order_ids);
        query_builder.push(") ORDER BY o.created_at DESC");

        let rows = query_builder.build()
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::database_error(format!("预加载订单关联数据失败: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }
}
