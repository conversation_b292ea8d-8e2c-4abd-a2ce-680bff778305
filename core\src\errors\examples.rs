//! 统一错误处理系统使用示例
//!
//! 展示如何使用新的统一错误处理系统

use super::unified::*;
use super::utils::*;
use std::collections::HashMap;

/// 示例：创建业务错误
pub fn create_business_error_example() -> UnifiedResult<()> {
    // 创建订单错误
    let order_error = UnifiedError::Business(BusinessError::Order {
        message: "订单金额超出限制".to_string(),
        order_id: None,
        details: HashMap::new(),
    });
    
    // 使用结构体创建策略错误
    let mut details = HashMap::new();
    details.insert("strategy_name".to_string(), "grid_strategy".to_string());
    details.insert("error_code".to_string(), "GRID_001".to_string());
    
    let strategy_error = UnifiedError::Business(BusinessError::Strategy {
        message: "网格策略初始化失败".to_string(),
        strategy_id: Some(uuid::Uuid::new_v4()),
        strategy_type: Some("asymmetric_grid".to_string()),
        details,
    });
    
    // 演示错误属性
    println!("订单错误严重程度: {:?}", order_error.severity());
    println!("策略错误是否可重试: {}", strategy_error.is_retryable());
    println!("策略错误代码: {}", strategy_error.error_code());
    println!("策略错误用户消息: {}", strategy_error.user_message());
    
    Ok(())
}

/// 示例：使用错误上下文
pub fn error_context_example() -> Result<(), EnhancedError> {
    // 创建错误上下文
    let context = ErrorContextBuilder::new(
        "examples",
        "error_context_example", 
        file!(),
        line!()
    )
    .user_id(uuid::Uuid::new_v4())
    .session_id("session_123".to_string())
    .metadata("operation".to_string(), "demo".to_string())
    .build();
    
    // 模拟一个可能失败的操作
    let result: UnifiedResult<String> = Err(UnifiedError::Business(BusinessError::Order {
        message: "模拟订单失败".to_string(),
        order_id: None,
        details: HashMap::new(),
    }));
    
    // 添加上下文信息
    result
        .map(|_| ())
        .with_context(context)
}

/// 示例：使用重试机制
pub async fn retry_example() -> UnifiedResult<String> {
    let retry_config = RetryConfig {
        max_attempts: 3,
        initial_delay_ms: 100,
        backoff_multiplier: 2.0,
        max_delay_ms: 1000,
        jitter: true,
    };
    
    let executor = RetryExecutor::new(retry_config);
    
    // 模拟一个可能失败的异步操作
    let mut attempt_count = 0;
    executor.async_retry(|| {
        attempt_count += 1;
        async move {
            if attempt_count < 3 {
                Err(UnifiedError::Technical(TechnicalError::Network {
                    message: "网络连接失败".to_string(),
                    endpoint: None,
                    status_code: None,
                    details: HashMap::new(),
                }))
            } else {
                Ok("操作成功".to_string())
            }
        }
    }).await
}

/// 示例：批量错误处理
pub fn batch_error_example() -> UnifiedResult<Vec<String>> {
    let mut batch_handler = BatchErrorHandler::new("批量处理示例".to_string());
    let mut results = Vec::new();
    
    // 模拟批量操作
    for i in 1..=5 {
        let operation_result: UnifiedResult<String> = if i % 2 == 0 {
            Ok(format!("操作 {} 成功", i))
        } else {
            Err(UnifiedError::Business(BusinessError::Order {
                message: format!("操作 {} 失败", i),
                order_id: None,
                details: HashMap::new(),
            }))
        };
        
        match operation_result {
            Ok(result) => results.push(result),
            Err(error) => batch_handler.add_error(error),
        }
    }
    
    // 检查是否有错误
    if batch_handler.has_errors() {
        println!("批量操作中有 {} 个错误", batch_handler.error_count());
        batch_handler.into_result(results)
    } else {
        Ok(results)
    }
}

/// 示例：错误转换
pub fn error_conversion_example() -> UnifiedResult<()> {
    // 从标准库错误转换
    let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
    let unified_error: UnifiedError = io_error.into();
    println!("IO错误转换: {}", unified_error);
    
    // 从JSON错误转换
    let json_error = serde_json::from_str::<serde_json::Value>("invalid json");
    match json_error {
        Err(e) => {
            let unified_error: UnifiedError = e.into();
            println!("JSON错误转换: {}", unified_error);
        },
        _ => {},
    }
    
    Ok(())
}

/// 示例：使用Result扩展方法
pub fn result_extensions_example() -> Option<String> {
    let result: UnifiedResult<String> = Err(UnifiedError::Business(BusinessError::Order {
        message: "订单处理失败".to_string(),
        order_id: None,
        details: HashMap::new(),
    }));
    
    // 记录错误并继续
    let option_result = result.log_error("订单处理");
    
    // 或者忽略错误，返回默认值
    let default_result: UnifiedResult<String> = Err(UnifiedError::Business(BusinessError::Order {
        message: "另一个错误".to_string(),
        order_id: None,
        details: HashMap::new(),
    }));
    let final_result = default_result.ignore_error("默认值".to_string());
    
    println!("最终结果: {}", final_result);
    option_result
}

/// 示例：完整的错误处理流程
pub async fn complete_error_handling_example() -> Result<String, EnhancedError> {
    // 1. 创建错误上下文
    let context = ErrorContextBuilder::new(
        "examples",
        "complete_error_handling_example",
        file!(),
        line!()
    )
    .user_id(uuid::Uuid::new_v4())
    .request_id("req_123".to_string())
    .build();
    
    // 2. 执行可能失败的操作
    let operation_result = perform_risky_operation().await;
    
    // 3. 添加上下文并处理错误
    operation_result
        .with_context(context)
        .map_err(|enhanced_error| {
            // 记录详细错误信息
            tracing::error!(
                "操作失败: {} (追踪ID: {})",
                enhanced_error,
                enhanced_error.trace_id
            );
            
            // 可以添加额外的元数据
            enhanced_error.with_metadata(
                "recovery_suggestion".to_string(),
                "请检查网络连接并重试".to_string()
            )
        })
}

/// 模拟一个可能失败的异步操作
async fn perform_risky_operation() -> UnifiedResult<String> {
    // 模拟网络请求失败
    Err(UnifiedError::Technical(TechnicalError::Network {
        message: "连接超时".to_string(),
        endpoint: Some("https://api.example.com".to_string()),
        status_code: None,
        details: {
            let mut details = HashMap::new();
            details.insert("timeout_ms".to_string(), "5000".to_string());
            details
        },
    }))
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_business_error_creation() {
        let result = create_business_error_example();
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_error_context() {
        let result = error_context_example();
        assert!(result.is_err());
        
        if let Err(enhanced_error) = result {
            assert_eq!(enhanced_error.context.module, "examples");
            assert_eq!(enhanced_error.context.function, "error_context_example");
            assert!(enhanced_error.context.user_id.is_some());
        }
    }
    
    #[tokio::test]
    async fn test_retry_mechanism() {
        let result = retry_example().await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "操作成功");
    }
    
    #[test]
    fn test_batch_error_handling() {
        let result = batch_error_example();
        assert!(result.is_err()); // 因为有一些操作失败了
        
        if let Err(error) = result {
            assert!(error.to_string().contains("errors occurred"));
        }
    }
    
    #[test]
    fn test_error_conversion() {
        let result = error_conversion_example();
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_result_extensions() {
        let result = result_extensions_example();
        assert!(result.is_none()); // 因为操作失败了
    }
    
    #[tokio::test]
    async fn test_complete_error_handling() {
        let result = complete_error_handling_example().await;
        assert!(result.is_err());
        
        if let Err(enhanced_error) = result {
            assert!(enhanced_error.context.metadata.contains_key("recovery_suggestion"));
        }
    }
}
