//! 日志指标收集器实现

use async_trait::async_trait;
use sigmax_core::{traits::MetricsCollector, SigmaXResult};
use std::collections::HashMap;
use std::time::Duration;
use tracing::{info, debug};

/// 日志指标收集器
/// 将指标记录到日志中，适用于开发和调试环境
pub struct LogMetricsCollector {
    prefix: String,
}

impl LogMetricsCollector {
    /// 创建新的日志指标收集器
    pub fn new(prefix: impl Into<String>) -> Self {
        Self {
            prefix: prefix.into(),
        }
    }
}

#[async_trait]
impl MetricsCollector for LogMetricsCollector {
    async fn record_risk_check(&self, passed: bool) {
        info!(
            target: "metrics",
            metric = %format!("{}.risk_check", self.prefix),
            passed = %passed,
            "Risk check recorded"
        );
    }

    async fn record_cache_hit(&self) {
        info!(
            target: "metrics",
            metric = %format!("{}.cache_hit", self.prefix),
            "Cache hit recorded"
        );
    }

    async fn record_latency(&self, operation: &str, duration: Duration) {
        info!(
            target: "metrics",
            metric = %format!("{}.latency.{}", self.prefix, operation),
            duration_ms = %duration.as_millis(),
            "Latency recorded"
        );
    }

    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        debug!("Log metrics collector does not support metrics export");
        // 返回空的指标集合，因为日志收集器不存储指标
        Ok(HashMap::new())
    }
}

impl Default for LogMetricsCollector {
    fn default() -> Self {
        Self::new("sigmax")
    }
}
