//! 统一错误处理模式
//!
//! 提供一致的错误处理机制，改善开发体验

use sigmax_core::{SigmaXResult, SigmaXError};

/// 错误上下文trait，用于为Option和Result添加上下文信息
pub trait ErrorContext<T> {
    /// 为错误添加上下文信息
    fn with_context(self, context: &str) -> SigmaXResult<T>;
    
    /// 为错误添加格式化上下文信息
    fn with_context_f<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce() -> String;
}

impl<T> ErrorContext<T> for Option<T> {
    fn with_context(self, context: &str) -> SigmaXResult<T> {
        self.ok_or_else(|| SigmaXError::NotFound(context.to_string()))
    }
    
    fn with_context_f<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce() -> String,
    {
        self.ok_or_else(|| SigmaXError::NotFound(f()))
    }
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: std::error::Error + Send + Sync + 'static,
{
    fn with_context(self, context: &str) -> SigmaXResult<T> {
        self.map_err(|e| SigmaXError::Internal(format!("{}: {}", context, e)))
    }
    
    fn with_context_f<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| SigmaXError::Internal(format!("{}: {}", f(), e)))
    }
}

/// 引擎特定错误处理
pub trait EngineErrorExt<T> {
    /// 转换为引擎配置错误
    fn to_config_error(self, message: &str) -> SigmaXResult<T>;
    
    /// 转换为引擎操作错误
    fn to_operation_error(self, operation: &str) -> SigmaXResult<T>;
    
    /// 转换为引擎超时错误
    fn to_timeout_error(self, timeout_ms: u64) -> SigmaXResult<T>;
}

impl<T, E> EngineErrorExt<T> for Result<T, E>
where
    E: std::error::Error + Send + Sync + 'static,
{
    fn to_config_error(self, message: &str) -> SigmaXResult<T> {
        self.map_err(|e| SigmaXError::Config(format!("{}: {}", message, e)))
    }
    
    fn to_operation_error(self, operation: &str) -> SigmaXResult<T> {
        self.map_err(|e| SigmaXError::InvalidOperation(format!("{} failed: {}", operation, e)))
    }
    
    fn to_timeout_error(self, timeout_ms: u64) -> SigmaXResult<T> {
        self.map_err(|_| SigmaXError::Internal(format!("Operation timed out after {}ms", timeout_ms)))
    }
}

/// 结果扩展trait，提供常用的错误处理方法
pub trait ResultExt<T> {
    /// 忽略错误，返回默认值
    fn ignore_error(self, default: T) -> T;
    
    /// 记录错误并继续
    fn log_error(self, context: &str) -> Option<T>;
    
    /// 重试操作
    fn retry<F>(f: F, max_attempts: usize) -> SigmaXResult<T>
    where
        F: Fn() -> SigmaXResult<T>;
}

impl<T> ResultExt<T> for SigmaXResult<T> {
    fn ignore_error(self, default: T) -> T {
        self.unwrap_or(default)
    }
    
    fn log_error(self, context: &str) -> Option<T> {
        match self {
            Ok(value) => Some(value),
            Err(e) => {
                tracing::error!("{}: {}", context, e);
                None
            }
        }
    }
    
    fn retry<F>(f: F, max_attempts: usize) -> SigmaXResult<T>
    where
        F: Fn() -> SigmaXResult<T>,
    {
        let mut last_error = None;
        
        for attempt in 1..=max_attempts {
            match f() {
                Ok(value) => return Ok(value),
                Err(e) => {
                    tracing::warn!("Attempt {} failed: {}", attempt, e);
                    last_error = Some(e);
                    
                    if attempt < max_attempts {
                        // 简单的退避策略
                        let delay = std::time::Duration::from_millis(100 * attempt as u64);
                        std::thread::sleep(delay);
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| {
            SigmaXError::Internal("All retry attempts failed".to_string())
        }))
    }
}

/// 异步结果扩展trait
pub trait AsyncResultExt<T> {
    /// 异步重试操作
    async fn async_retry<F, Fut>(f: F, max_attempts: usize) -> SigmaXResult<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<T>>;
}

impl<T> AsyncResultExt<T> for SigmaXResult<T> {
    async fn async_retry<F, Fut>(f: F, max_attempts: usize) -> SigmaXResult<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<T>>,
    {
        let mut last_error = None;
        
        for attempt in 1..=max_attempts {
            match f().await {
                Ok(value) => return Ok(value),
                Err(e) => {
                    tracing::warn!("Async attempt {} failed: {}", attempt, e);
                    last_error = Some(e);
                    
                    if attempt < max_attempts {
                        // 异步退避策略
                        let delay = std::time::Duration::from_millis(100 * attempt as u64);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| {
            SigmaXError::Internal("All async retry attempts failed".to_string())
        }))
    }
}

/// 批量错误处理
pub struct BatchErrorHandler {
    errors: Vec<SigmaXError>,
    context: String,
}

impl BatchErrorHandler {
    /// 创建新的批量错误处理器
    pub fn new(context: String) -> Self {
        Self {
            errors: Vec::new(),
            context,
        }
    }
    
    /// 添加错误
    pub fn add_error(&mut self, error: SigmaXError) {
        self.errors.push(error);
    }
    
    /// 添加结果，如果是错误则收集
    pub fn add_result<T>(&mut self, result: SigmaXResult<T>) -> Option<T> {
        match result {
            Ok(value) => Some(value),
            Err(e) => {
                self.add_error(e);
                None
            }
        }
    }
    
    /// 检查是否有错误
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }
    
    /// 获取错误数量
    pub fn error_count(&self) -> usize {
        self.errors.len()
    }
    
    /// 生成汇总错误
    pub fn into_result<T>(self, success_value: T) -> SigmaXResult<T> {
        if self.errors.is_empty() {
            Ok(success_value)
        } else {
            let error_messages: Vec<String> = self.errors
                .into_iter()
                .map(|e| e.to_string())
                .collect();
            
            Err(SigmaXError::Internal(format!(
                "{}: {} errors occurred: [{}]",
                self.context,
                error_messages.len(),
                error_messages.join(", ")
            )))
        }
    }
    
    /// 记录所有错误并返回结果
    pub fn log_and_continue<T>(self, success_value: T) -> T {
        if !self.errors.is_empty() {
            for error in &self.errors {
                tracing::error!("{}: {}", self.context, error);
            }
        }
        success_value
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sigmax_core::Amount;

    #[test]
    fn test_error_context() {
        let option: Option<i32> = None;
        let result = option.with_context("Test context");
        assert!(result.is_err());
        
        let option: Option<i32> = Some(42);
        let result = option.with_context("Test context");
        assert_eq!(result.unwrap(), 42);
    }

    #[test]
    fn test_result_ext() {
        let result: SigmaXResult<i32> = Ok(42);
        assert_eq!(result.ignore_error(0), 42);
        
        let result: SigmaXResult<i32> = Err(SigmaXError::Generic("test".to_string()));
        assert_eq!(result.ignore_error(0), 0);
    }

    #[test]
    fn test_batch_error_handler() {
        let mut handler = BatchErrorHandler::new("Test operation".to_string());
        
        handler.add_result(Ok::<i32, SigmaXError>(42));
        handler.add_result(Err::<i32, SigmaXError>(SigmaXError::Generic("error1".to_string())));
        handler.add_result(Err::<i32, SigmaXError>(SigmaXError::Generic("error2".to_string())));
        
        assert!(handler.has_errors());
        assert_eq!(handler.error_count(), 2);
        
        let result = handler.into_result(());
        assert!(result.is_err());
    }

    #[test]
    fn test_retry_mechanism() {
        let mut attempt_count = 0;
        
        let result = SigmaXResult::retry(|| {
            attempt_count += 1;
            if attempt_count < 3 {
                Err(SigmaXError::Generic("retry test".to_string()))
            } else {
                Ok(42)
            }
        }, 5);
        
        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempt_count, 3);
    }
}