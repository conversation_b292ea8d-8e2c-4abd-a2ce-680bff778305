//! 统一的引擎配置系统
//!
//! 基于核心设计原则的配置管理：
//! - 关注点分离：配置与实现分离
//! - 类型安全：强类型配置系统
//! - 单一职责：每个配置类专注特定功能

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

use crate::{Amount, EngineType, TradingPair, ExchangeId, SigmaXResult, SigmaXError};

// ============================================================================
// 统一引擎配置 - 替代所有重复的配置定义
// ============================================================================

/// 统一引擎配置
/// 
/// 设计原则：
/// - 类型安全：强类型字段定义
/// - 可扩展：支持引擎特定配置
/// - 验证友好：内置验证逻辑
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedEngineConfig {
    // ========================================================================
    // 基础配置
    // ========================================================================
    
    /// 引擎ID
    pub engine_id: Uuid,
    /// 引擎类型
    pub engine_type: EngineType,
    /// 引擎名称
    pub name: String,
    /// 描述
    pub description: Option<String>,
    
    // ========================================================================
    // 交易配置
    // ========================================================================
    
    /// 交易对列表
    pub trading_pairs: Vec<TradingPair>,
    /// 初始资金
    pub initial_capital: Amount,
    
    // ========================================================================
    // 交易所配置
    // ========================================================================
    
    /// 交易所配置
    pub exchange_config: Option<ExchangeConfig>,
    
    // ========================================================================
    // 风险控制配置
    // ========================================================================
    
    /// 风险控制配置
    pub risk_config: RiskConfig,
    
    // ========================================================================
    // 性能配置
    // ========================================================================
    
    /// 性能配置
    pub performance_config: PerformanceConfig,
    
    // ========================================================================
    // 引擎特定配置
    // ========================================================================
    
    /// 引擎特定设置（JSON格式）
    pub engine_specific: HashMap<String, serde_json::Value>,
    
    // ========================================================================
    // 元数据
    // ========================================================================
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

// ============================================================================
// 子配置结构
// ============================================================================

/// 交易所配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExchangeConfig {
    /// 交易所ID
    pub exchange_id: ExchangeId,
    /// API密钥
    pub api_key: Option<String>,
    /// API密钥密码
    pub api_secret: Option<String>,
    /// 是否启用沙盒模式
    pub sandbox: bool,
    /// API请求超时时间（毫秒）
    pub timeout_ms: u64,
    /// 每秒最大请求数
    pub rate_limit: u32,
}

/// 风险控制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    /// 最大持仓金额
    pub max_position_size: Option<Amount>,
    /// 每秒最大订单数
    pub max_orders_per_second: Option<u32>,
    /// 风险检查超时时间（毫秒）
    pub risk_check_timeout_ms: Option<u64>,
    /// 启用风险控制
    pub enabled: bool,
}

/// 性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 最大并发订单数
    pub max_concurrent_orders: usize,
    /// 批处理大小
    pub batch_size: Option<usize>,
    /// 模拟延迟（毫秒）
    pub simulate_latency_ms: Option<u64>,
    /// 启用性能监控
    pub enable_monitoring: bool,
}

// ============================================================================
// 配置构建器 - 支持流式API
// ============================================================================

/// 统一引擎配置构建器
pub struct UnifiedEngineConfigBuilder {
    config: UnifiedEngineConfig,
}

impl UnifiedEngineConfigBuilder {
    /// 创建新的配置构建器
    pub fn new(engine_type: EngineType, name: String) -> Self {
        let now = Utc::now();
        Self {
            config: UnifiedEngineConfig {
                engine_id: Uuid::new_v4(),
                engine_type,
                name,
                description: None,
                trading_pairs: Vec::new(),
                initial_capital: Amount::ZERO,
                exchange_config: None,
                risk_config: RiskConfig::default(),
                performance_config: PerformanceConfig::default(),
                engine_specific: HashMap::new(),
                created_at: now,
                updated_at: now,
            },
        }
    }
    
    /// 设置交易对
    pub fn trading_pairs(mut self, pairs: Vec<TradingPair>) -> Self {
        self.config.trading_pairs = pairs;
        self
    }
    
    /// 设置初始资金
    pub fn initial_capital(mut self, capital: Amount) -> Self {
        self.config.initial_capital = capital;
        self
    }
    
    /// 设置交易所配置
    pub fn exchange_config(mut self, config: ExchangeConfig) -> Self {
        self.config.exchange_config = Some(config);
        self
    }
    
    /// 设置风险配置
    pub fn risk_config(mut self, config: RiskConfig) -> Self {
        self.config.risk_config = config;
        self
    }
    
    /// 设置性能配置
    pub fn performance_config(mut self, config: PerformanceConfig) -> Self {
        self.config.performance_config = config;
        self
    }
    
    /// 添加引擎特定配置
    pub fn engine_specific<T: Serialize>(mut self, key: String, value: T) -> SigmaXResult<Self> {
        let json_value = serde_json::to_value(value)
            .map_err(|e| SigmaXError::Config(format!("Failed to serialize engine specific config: {}", e)))?;
        self.config.engine_specific.insert(key, json_value);
        Ok(self)
    }
    
    /// 构建配置
    pub fn build(self) -> SigmaXResult<UnifiedEngineConfig> {
        self.config.validate()?;
        Ok(self.config)
    }
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for ExchangeConfig {
    fn default() -> Self {
        Self {
            exchange_id: ExchangeId::Simulator,
            api_key: None,
            api_secret: None,
            sandbox: true,
            timeout_ms: 30000,
            rate_limit: 1200,
        }
    }
}

impl Default for RiskConfig {
    fn default() -> Self {
        Self {
            max_position_size: None,
            max_orders_per_second: Some(10),
            risk_check_timeout_ms: Some(100),
            enabled: true,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_concurrent_orders: 100,
            batch_size: Some(1000),
            simulate_latency_ms: None,
            enable_monitoring: true,
        }
    }
}

// ============================================================================
// 配置验证
// ============================================================================

impl UnifiedEngineConfig {
    /// 验证配置
    pub fn validate(&self) -> SigmaXResult<()> {
        // 验证基础字段
        if self.name.is_empty() {
            return Err(SigmaXError::Config("Engine name cannot be empty".to_string()));
        }
        
        if self.trading_pairs.is_empty() {
            return Err(SigmaXError::Config("At least one trading pair is required".to_string()));
        }
        
        if self.initial_capital <= Amount::ZERO {
            return Err(SigmaXError::Config("Initial capital must be positive".to_string()));
        }
        
        // 验证交易对
        for pair in &self.trading_pairs {
            if pair.base.is_empty() || pair.quote.is_empty() {
                return Err(SigmaXError::Config("Trading pair base and quote cannot be empty".to_string()));
            }
        }
        
        // 验证风险配置
        if let Some(max_pos) = self.risk_config.max_position_size {
            if max_pos <= Amount::ZERO {
                return Err(SigmaXError::Config("Max position size must be positive".to_string()));
            }
        }
        
        // 验证性能配置
        if self.performance_config.max_concurrent_orders == 0 {
            return Err(SigmaXError::Config("Max concurrent orders must be greater than 0".to_string()));
        }
        
        // 引擎类型特定验证
        match self.engine_type {
            EngineType::Live => {
                if self.exchange_config.is_none() {
                    return Err(SigmaXError::Config("Live engine requires exchange configuration".to_string()));
                }
            }
            _ => {}
        }
        
        Ok(())
    }
}
