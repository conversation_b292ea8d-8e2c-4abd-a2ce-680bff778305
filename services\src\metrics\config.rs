//! 指标收集配置

use serde::{Deserialize, Serialize};

/// 指标收集器类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum MetricsCollectorType {
    /// Prometheus 指标收集器
    Prometheus,
    /// 日志指标收集器
    Log,
    /// 聚合指标收集器
    Aggregator,
}

/// 指标收集配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MetricsConfig {
    /// 是否启用指标收集
    pub enabled: bool,
    /// 指标收集器类型
    pub collector_type: MetricsCollectorType,
    /// 指标前缀
    pub prefix: String,
    /// Prometheus 配置
    pub prometheus: Option<PrometheusConfig>,
    /// 日志配置
    pub log: Option<LogConfig>,
}

/// Prometheus 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrometheusConfig {
    /// 监听地址
    pub listen_address: String,
    /// 监听端口
    pub listen_port: u16,
    /// 指标路径
    pub metrics_path: String,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    /// 日志级别
    pub level: String,
    /// 日志目标
    pub target: String,
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            collector_type: MetricsCollectorType::Log,
            prefix: "sigmax".to_string(),
            prometheus: Some(PrometheusConfig::default()),
            log: Some(LogConfig::default()),
        }
    }
}

impl Default for PrometheusConfig {
    fn default() -> Self {
        Self {
            listen_address: "127.0.0.1".to_string(),
            listen_port: 9090,
            metrics_path: "/metrics".to_string(),
        }
    }
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            target: "metrics".to_string(),
        }
    }
}
