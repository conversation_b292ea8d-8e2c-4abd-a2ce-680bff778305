//! 数据模块常量定义
//!
//! 统一管理数据处理相关的常量，避免魔法数字

/// 数据质量评估常量
pub mod quality {
    /// 延迟阈值 - 优秀级别 (毫秒)
    pub const LATENCY_THRESHOLD_EXCELLENT: u64 = 100;
    
    /// 延迟阈值 - 良好级别 (毫秒)
    pub const LATENCY_THRESHOLD_GOOD: u64 = 500;
    
    /// 延迟阈值 - 可接受级别 (毫秒)
    pub const LATENCY_THRESHOLD_ACCEPTABLE: u64 = 1000;
    
    /// 质量分数 - 优秀级别
    pub const QUALITY_SCORE_EXCELLENT: f64 = 1.0;
    
    /// 质量分数 - 良好级别
    pub const QUALITY_SCORE_GOOD: f64 = 0.95;
    
    /// 质量分数 - 可接受级别
    pub const QUALITY_SCORE_ACCEPTABLE: f64 = 0.8;
    
    /// 质量分数 - 较差级别
    pub const QUALITY_SCORE_POOR: f64 = 0.6;
    
    /// 质量分数 - 最低级别
    pub const QUALITY_SCORE_MINIMUM: f64 = 0.3;
}

/// 数据提供商配置常量
pub mod provider {
    /// 默认重试次数
    pub const DEFAULT_RETRY_COUNT: u32 = 3;
    
    /// 默认超时时间 (毫秒)
    pub const DEFAULT_TIMEOUT_MS: u64 = 5000;
    
    /// 默认连接池大小
    pub const DEFAULT_POOL_SIZE: u32 = 10;
    
    /// 最大连接池大小
    pub const MAX_POOL_SIZE: u32 = 100;
    
    /// 连接超时时间 (毫秒)
    pub const CONNECTION_TIMEOUT_MS: u64 = 3000;
    
    /// 请求超时时间 (毫秒)
    pub const REQUEST_TIMEOUT_MS: u64 = 10000;
}

/// 缓存配置常量
pub mod cache {
    /// 默认缓存容量
    pub const DEFAULT_CAPACITY: usize = 1000;
    
    /// 最大缓存容量
    pub const MAX_CAPACITY: usize = 100000;
    
    /// 默认TTL (秒)
    pub const DEFAULT_TTL_SECONDS: u64 = 300;
    
    /// 最大TTL (秒)
    pub const MAX_TTL_SECONDS: u64 = 86400; // 24小时
    
    /// 清理间隔 (秒)
    pub const CLEANUP_INTERVAL_SECONDS: u64 = 60;
}

/// 数据验证常量
pub mod validation {
    /// 最大价格变化百分比 (用于异常检测)
    pub const MAX_PRICE_CHANGE_PERCENT: f64 = 50.0;
    
    /// 最小价格值
    pub const MIN_PRICE_VALUE: f64 = 0.000001;
    
    /// 最大价格值
    pub const MAX_PRICE_VALUE: f64 = 1000000.0;
    
    /// 最大成交量
    pub const MAX_VOLUME: f64 = 1000000000.0;
    
    /// 价格精度 (小数位数)
    pub const PRICE_PRECISION: u32 = 8;
    
    /// 成交量精度 (小数位数)
    pub const VOLUME_PRECISION: u32 = 6;
}

/// 时间相关常量
pub mod time {
    /// 一秒的毫秒数
    pub const MILLISECONDS_PER_SECOND: u64 = 1000;
    
    /// 一分钟的毫秒数
    pub const MILLISECONDS_PER_MINUTE: u64 = 60 * MILLISECONDS_PER_SECOND;
    
    /// 一小时的毫秒数
    pub const MILLISECONDS_PER_HOUR: u64 = 60 * MILLISECONDS_PER_MINUTE;
    
    /// 一天的毫秒数
    pub const MILLISECONDS_PER_DAY: u64 = 24 * MILLISECONDS_PER_HOUR;
    
    /// 默认时间窗口大小 (毫秒)
    pub const DEFAULT_TIME_WINDOW_MS: u64 = 5 * MILLISECONDS_PER_MINUTE;
}

/// 网络相关常量
pub mod network {
    /// 默认重连间隔 (毫秒)
    pub const DEFAULT_RECONNECT_INTERVAL_MS: u64 = 1000;
    
    /// 最大重连间隔 (毫秒)
    pub const MAX_RECONNECT_INTERVAL_MS: u64 = 30000;
    
    /// 重连指数退避因子
    pub const RECONNECT_BACKOFF_FACTOR: f64 = 1.5;
    
    /// 心跳间隔 (毫秒)
    pub const HEARTBEAT_INTERVAL_MS: u64 = 30000;
    
    /// 连接保活时间 (毫秒)
    pub const KEEP_ALIVE_MS: u64 = 60000;
}
