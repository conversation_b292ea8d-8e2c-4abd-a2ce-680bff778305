//! SigmaX Core Module
//!
//! 包含核心接口、数据模型和枚举，是系统稳定性的基石。

pub mod types;
pub mod models;
pub mod enums;
pub mod traits;
pub mod errors;
pub mod config_cache;
pub mod events;
pub mod services;
pub mod config;
pub mod logging;
pub mod startup;
pub mod validation;
pub mod sqlx_helpers;
pub mod constants;

#[cfg(test)]
mod models_migration_test;

// 重新导出主要类型 - 注意导入顺序，避免类型冲突
pub use enums::*;        // 枚举类型优先
pub use types::*;        // 基础类型
pub use models::*;       // 数据模型
pub use traits::*;       // trait定义
pub use errors::*;       // 错误类型
pub use config_cache::ConfigCache;
pub use events::*;       // 事件类型
pub use services::*;     // 服务类型
pub use config::*;       // 配置类型
pub use logging::*;      // 日志类型
pub use startup::*;      // 启动相关
pub use validation::*;   // 验证相关
// 常量模块通过 mod constants 已经可用，不需要重新导出
