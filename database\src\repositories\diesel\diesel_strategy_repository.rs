//! Diesel 策略仓储实现

use std::sync::Arc;
use diesel::prelude::*;
use sigmax_core::{
    StrategyId, StrategyState, StrategyInfo, SigmaXResult, SigmaXError, TradingPair
};

use crate::repositories::traits::StrategyRepository;
use crate::diesel_models::*;
use crate::schema::*;
use super::{DbPool, DbConnection};

/// Diesel 策略仓库实现
pub struct DieselStrategyRepository {
    pool: Arc<DbPool>,
}

impl DieselStrategyRepository {
    pub fn new(pool: Arc<DbPool>) -> Self {
        Self { pool }
    }
    
    fn get_connection(&self) -> SigmaXResult<DbConnection> {
        self.pool.get()
            .map_err(|e| SigmaXError::database_error(format!("Failed to get database connection: {}", e)))
    }
}

impl StrategyRepository for DieselStrategyRepository {
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()> {
        let mut conn = self.get_connection()?;
        let new_strategy = NewStrategy {
            id: strategy_id,
            name: "Strategy".to_string(), // 简化处理
            strategy_type: "Grid".to_string(),
            config: state.config.clone(),
            status: format!("{:?}", state.status),
            created_at: state.created_at,
            updated_at: state.updated_at,
        };
        
        diesel::insert_into(strategies::table)
            .values(&new_strategy)
            .on_conflict(strategies::id)
            .do_update()
            .set((
                strategies::config.eq(&new_strategy.config),
                strategies::status.eq(&new_strategy.status),
                strategies::updated_at.eq(&new_strategy.updated_at),
            ))
            .execute(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to save strategy state: {}", e)))?;
        
        Ok(())
    }
    
    async fn load_strategy_state(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        let mut conn = self.get_connection()?;
        
        let strategy_model = strategies::table
            .filter(strategies::id.eq(strategy_id))
            .first::<StrategyModel>(&mut conn)
            .optional()
            .map_err(|e| SigmaXError::database_error(format!("Failed to load strategy state: {}", e)))?;
        
        if let Some(model) = strategy_model {
            let status = match model.status.as_str() {
                "Created" => sigmax_core::StrategyStatus::Created,
                "Running" => sigmax_core::StrategyStatus::Running,
                "Paused" => sigmax_core::StrategyStatus::Paused,
                "Stopped" => sigmax_core::StrategyStatus::Stopped,
                "Error" => sigmax_core::StrategyStatus::Error,
                _ => sigmax_core::StrategyStatus::Created,
            };
            
            Ok(Some(StrategyState {
                strategy_id: model.id,
                status,
                config: model.config,
                state_data: serde_json::Value::Null,
                created_at: model.created_at,
                updated_at: model.updated_at,
            }))
        } else {
            Ok(None)
        }
    }
    
    async fn get_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>> {
        let mut conn = self.get_connection()?;
        
        let strategy_models = strategies::table
            .order(strategies::created_at.desc())
            .load::<StrategyModel>(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to get all strategies: {}", e)))?;
        
        let strategies = strategy_models.into_iter().map(|model| {
            let status = match model.status.as_str() {
                "Created" => sigmax_core::StrategyStatus::Created,
                "Running" => sigmax_core::StrategyStatus::Running,
                "Paused" => sigmax_core::StrategyStatus::Paused,
                "Stopped" => sigmax_core::StrategyStatus::Stopped,
                "Error" => sigmax_core::StrategyStatus::Error,
                _ => sigmax_core::StrategyStatus::Created,
            };
            
            StrategyInfo {
                id: model.id,
                name: model.name,
                strategy_type: model.strategy_type,
                status,
                trading_pair: TradingPair::new("BTC", "USDT"), // 简化处理
                created_at: model.created_at,
                updated_at: model.updated_at,
            }
        }).collect();
        
        Ok(strategies)
    }
}
