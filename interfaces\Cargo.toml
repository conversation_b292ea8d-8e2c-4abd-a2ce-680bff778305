[package]
name = "sigmax-interfaces"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
# Core dependencies
sigmax-core.workspace = true
async-trait.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
uuid.workspace = true
chrono.workspace = true
rust_decimal.workspace = true
thiserror.workspace = true
tracing.workspace = true

[dev-dependencies]
tokio-test = "0.4"