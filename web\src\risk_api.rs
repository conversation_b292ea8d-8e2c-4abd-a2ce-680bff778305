//! 统一风险管理API
//!
//! 按照最佳实践设计的统一风险管理API接口
//! 整合了所有风控功能，提供完整的企业级风险管理能力

use axum::{
    routing::{get, post, put, delete, patch},
    Router,
};

use crate::AppState;
use crate::handlers::risk_handlers::{
    // 风险检查处理器
    check_order_risk,
    check_position_risk,
    check_portfolio_risk,
    check_batch_risk,
    smart_risk_assessment,
    
    // 规则管理处理器
    list_rules,
    create_rule,
    get_rule,
    update_rule,
    delete_rule,
    toggle_rule,
    get_rule_history,
    get_rule_templates,
    
    // 监控处理器
    get_real_time_metrics,
    get_risk_trends,
    get_alerts,
    create_alert_rule,
    get_risk_dashboard,
    
    // 配置处理器
    get_risk_parameters,
    update_risk_parameters,
    get_risk_config,
    update_risk_config,
    
    // 分析处理器
    run_stress_test,
    run_monte_carlo_simulation,
    run_sensitivity_analysis,
    get_analysis_history,
    
    // 系统处理器
    health_check,
    get_system_status,
};

/// 创建统一风险管理API路由
///
/// 这是SigmaX-R系统的统一风险管理API，整合了所有风险管理功能：
/// - 风险检查：订单、持仓、投资组合风险评估
/// - 规则管理：风险规则的CRUD操作
/// - 实时监控：风险指标监控和预警
/// - 配置管理：风险参数配置
/// - 分析工具：压力测试、蒙特卡洛模拟等
/// - 系统状态：健康检查和状态监控
pub fn create_unified_risk_api() -> Router<AppState> {
    Router::new()
        // 核心风险检查 - 动作导向
        .nest("/check", create_risk_check_routes())
        // 规则管理 - 资源导向
        .nest("/rules", create_rule_management_routes())
        // 实时监控 - 查询导向
        .nest("/monitoring", create_monitoring_routes())
        // 配置管理 - 配置导向
        .nest("/config", create_config_routes())
        // 分析工具 - 分析导向
        .nest("/analysis", create_analysis_routes())
        // 系统状态
        .nest("/system", create_system_routes())
}

/// 风险检查路由
fn create_risk_check_routes() -> Router<AppState> {
    Router::new()
        .route("/order", post(check_order_risk))
        .route("/position", post(check_position_risk))
        .route("/portfolio", post(check_portfolio_risk))
        .route("/batch", post(check_batch_risk))
        .route("/smart", post(smart_risk_assessment))
}

/// 规则管理路由
fn create_rule_management_routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_rules).post(create_rule))
        .route("/:id", get(get_rule).put(update_rule).delete(delete_rule))
        .route("/:id/toggle", patch(toggle_rule))
        .route("/:id/history", get(get_rule_history))
        .route("/templates", get(get_rule_templates))
}

/// 监控路由
fn create_monitoring_routes() -> Router<AppState> {
    Router::new()
        .route("/metrics", get(get_real_time_metrics))
        .route("/trends", get(get_risk_trends))
        .route("/alerts", get(get_alerts).post(create_alert_rule))
        .route("/dashboard", get(get_risk_dashboard))
}

/// 配置管理路由
fn create_config_routes() -> Router<AppState> {
    Router::new()
        .route("/parameters", get(get_risk_parameters).put(update_risk_parameters))
        .route("/", get(get_risk_config).put(update_risk_config))
}

/// 分析工具路由
fn create_analysis_routes() -> Router<AppState> {
    Router::new()
        .route("/stress-test", post(run_stress_test))
        .route("/monte-carlo", post(run_monte_carlo_simulation))
        .route("/sensitivity", post(run_sensitivity_analysis))
        .route("/history", get(get_analysis_history))
}

/// 系统状态路由
fn create_system_routes() -> Router<AppState> {
    Router::new()
        .route("/health", get(health_check))
        .route("/status", get(get_system_status))
}

/// API路径常量
///
/// 统一风险管理API的所有路径常量，便于前端和其他服务调用
pub mod paths {
    /// API基础路径
    pub const API_BASE: &str = "/api/risk";

    // ============================================================================
    // 风险检查相关路径
    // ============================================================================
    pub const CHECK_ORDER: &str = "/api/risk/check/order";
    pub const CHECK_POSITION: &str = "/api/risk/check/position";
    pub const CHECK_PORTFOLIO: &str = "/api/risk/check/portfolio";
    pub const CHECK_BATCH: &str = "/api/risk/check/batch";
    pub const SMART_ASSESSMENT: &str = "/api/risk/check/smart";

    // ============================================================================
    // 规则管理相关路径
    // ============================================================================
    pub const RULES: &str = "/api/risk/rules";
    pub const RULE_BY_ID: &str = "/api/risk/rules/:id";
    pub const RULE_TOGGLE: &str = "/api/risk/rules/:id/toggle";
    pub const RULE_HISTORY: &str = "/api/risk/rules/:id/history";
    pub const RULE_TEMPLATES: &str = "/api/risk/rules/templates";

    // ============================================================================
    // 监控相关路径
    // ============================================================================
    pub const METRICS: &str = "/api/risk/monitoring/metrics";
    pub const TRENDS: &str = "/api/risk/monitoring/trends";
    pub const ALERTS: &str = "/api/risk/monitoring/alerts";
    pub const DASHBOARD: &str = "/api/risk/monitoring/dashboard";

    // ============================================================================
    // 配置管理相关路径
    // ============================================================================
    pub const PARAMETERS: &str = "/api/risk/config/parameters";
    pub const CONFIG: &str = "/api/risk/config";

    // ============================================================================
    // 分析工具相关路径
    // ============================================================================
    pub const STRESS_TEST: &str = "/api/risk/analysis/stress-test";
    pub const MONTE_CARLO: &str = "/api/risk/analysis/monte-carlo";
    pub const SENSITIVITY: &str = "/api/risk/analysis/sensitivity";
    pub const ANALYSIS_HISTORY: &str = "/api/risk/analysis/history";

    // ============================================================================
    // 系统状态相关路径
    // ============================================================================
    pub const HEALTH: &str = "/api/risk/system/health";
    pub const STATUS: &str = "/api/risk/system/status";
}

/// API版本信息
pub mod version {
    pub const VERSION: &str = "1.0.0";
    pub const API_VERSION: &str = "unified";
    pub const DESCRIPTION: &str = "统一风险管理API - 企业级风险管理解决方案";

    /// 获取API信息
    pub fn get_api_info() -> serde_json::Value {
        serde_json::json!({
            "version": VERSION,
            "api_version": API_VERSION,
            "description": DESCRIPTION,
            "features": [
                "统一的风险检查接口",
                "智能风险评估",
                "实时风险监控",
                "规则管理",
                "高级分析工具",
                "完整的可观测性",
                "CVaR风险度量",
                "压力测试",
                "蒙特卡洛模拟",
                "实时预警系统"
            ],
            "endpoints": {
                "check": "风险检查相关接口",
                "rules": "规则管理相关接口",
                "monitoring": "监控相关接口",
                "config": "配置相关接口",
                "analysis": "分析工具相关接口",
                "system": "系统状态相关接口"
            }
        })
    }
}
