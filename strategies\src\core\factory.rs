//! 策略工厂
//!
//! 提供简单直接的策略创建功能，避免复杂的注册系统

use std::sync::Arc;
use serde_json::Value;
use tracing::{info, error};
use thiserror::Error;
use sigmax_core::{SigmaXResult, SigmaXError, Strategy};
use crate::core::{StrategyServices, strategy_type::StrategyType};
use crate::asymmetric_grid::{AsymmetricVolatilityGridStrategy, AsymmetricGridConfig};

/// 策略工厂
///
/// 采用简单的匹配模式创建策略，避免复杂的注册系统
///
/// ## 设计原则
/// - 简单直接：使用match语句而不是复杂的注册表
/// - 易于扩展：添加新策略只需要添加一个match分支
/// - 类型安全：编译时检查策略类型
pub struct StrategyFactory;

impl StrategyFactory {
    /// 创建策略实例（类型安全版本）
    ///
    /// # 参数
    /// - `strategy_type`: 强类型的策略类型
    /// - `config`: 策略配置（JSON格式）
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// - `Ok(Arc<dyn Strategy>)`: 创建成功的策略实例
    /// - `Err(SigmaXError)`: 创建失败
    ///
    /// # 示例
    /// ```rust
    /// let strategy = StrategyFactory::create_strategy_typed(
    ///     StrategyType::AsymmetricVolatilityGrid,
    ///     config_json,
    ///     services,
    /// ).await?;
    /// ```
    pub async fn create_strategy_typed(
        strategy_type: StrategyType,
        config: Value,
        services: StrategyServices,
    ) -> SigmaXResult<Arc<dyn Strategy>> {
        info!("创建策略: type={:?}", strategy_type);

        // 验证服务容器
        services.validate()?;

        match strategy_type {
            StrategyType::AsymmetricVolatilityGrid => {
                Self::create_asymmetric_volatility_grid_strategy_from_json(config, services).await
            }
            // 🔥 未来添加新策略只需要在这里添加新的match分支
            // StrategyType::DollarCostAveraging => {
            //     Self::create_dca_strategy_from_json(config, services).await
            // }
            // StrategyType::Momentum => {
            //     Self::create_momentum_strategy_from_json(config, services).await
            // }
            // StrategyType::MeanReversion => {
            //     Self::create_mean_reversion_strategy_from_json(config, services).await
            // }
            _ => {
                error!("不支持的策略类型: {:?}", strategy_type);
                Err(FactoryError::unsupported_type(strategy_type).into())
            }
        }
    }

    /// 使用强类型配置创建非对称波动率网格策略
    ///
    /// # 参数
    /// - `config`: 强类型的策略配置
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// - `Ok(Arc<dyn Strategy>)`: 创建成功的策略实例
    /// - `Err(SigmaXError)`: 创建失败
    ///
    /// # 示例
    /// ```rust
    /// let config = AsymmetricGridConfig::balanced();
    /// let strategy = StrategyFactory::create_asymmetric_volatility_grid_strategy(
    ///     config,
    ///     services,
    /// ).await?;
    /// ```
    pub async fn create_asymmetric_volatility_grid_strategy(
        config: AsymmetricGridConfig,
        services: StrategyServices,
    ) -> SigmaXResult<Arc<dyn Strategy>> {
        info!("使用强类型配置创建非对称波动率网格策略");

        let strategy = AsymmetricVolatilityGridStrategy::builder()
            .with_config(config)
            .with_services(services)
            .build()
            .await?;

        Ok(Arc::new(strategy))
    }

    /// 创建策略实例（向后兼容版本）
    ///
    /// # 参数
    /// - `strategy_type`: 策略类型标识
    /// - `config`: 策略配置（JSON格式）
    /// - `services`: 策略服务容器
    ///
    /// # 返回
    /// - `Ok(Arc<dyn Strategy>)`: 创建成功的策略实例
    /// - `Err(SigmaXError)`: 创建失败
    ///
    /// # 示例
    /// ```rust
    /// let strategy = StrategyFactory::create_strategy(
    ///     "asymmetric_volatility_grid",
    ///     config_json,
    ///     services,
    /// ).await?;
    /// ```
    pub async fn create_strategy(
        strategy_type: &str,
        config: Value,
        services: StrategyServices,
    ) -> SigmaXResult<Arc<dyn Strategy>> {
        info!("创建策略: type={}", strategy_type);

        // 转换为强类型
        let typed_strategy_type = strategy_type.parse::<StrategyType>()?;

        // 使用类型安全的方法
        Self::create_strategy_typed(typed_strategy_type, config, services).await
    }

    /// 从JSON配置创建非对称波动率网格策略（内部方法）
    async fn create_asymmetric_volatility_grid_strategy_from_json(
        config: Value,
        services: StrategyServices,
    ) -> SigmaXResult<Arc<dyn Strategy>> {
        // 解析配置
        let strategy_config: AsymmetricGridConfig = serde_json::from_value(config)
            .map_err(FactoryError::from)?;

        // 使用强类型方法创建策略
        Self::create_asymmetric_volatility_grid_strategy(strategy_config, services).await
    }

    /// 获取支持的策略类型列表（字符串版本，向后兼容）
    ///
    /// # 返回
    /// 支持的策略类型标识列表
    pub fn supported_strategies() -> Vec<&'static str> {
        StrategyType::all_str()
    }

    /// 获取支持的策略类型列表（强类型版本）
    ///
    /// # 返回
    /// 支持的策略类型列表
    pub fn supported_strategy_types() -> Vec<StrategyType> {
        StrategyType::all()
    }

    /// 检查策略类型是否支持
    ///
    /// # 参数
    /// - `strategy_type`: 策略类型标识
    ///
    /// # 返回
    /// - `true`: 支持该策略类型
    /// - `false`: 不支持该策略类型
    pub fn is_supported(strategy_type: &str) -> bool {
        StrategyType::is_supported(strategy_type)
    }

    // 🔥 未来添加新策略时，在这里添加对应的创建方法
    // async fn create_dca_strategy(
    //     config: Value,
    //     services: StrategyServices,
    // ) -> SigmaXResult<Box<dyn Strategy>> {
    //     // 实现DCA策略创建逻辑
    // }
}

/// 策略工厂错误
///
/// 提供结构化的、类型安全的错误处理，包含丰富的上下文信息
#[derive(Error, Debug)]
pub enum FactoryError {
    /// 不支持的策略类型
    #[error("不支持的策略类型: {strategy_type:?}. 支持的类型: {supported_types:?}")]
    UnsupportedType {
        strategy_type: StrategyType,
        supported_types: Vec<StrategyType>,
    },

    /// 策略配置解析失败
    #[error("策略配置解析失败: {source}")]
    InvalidConfig {
        #[from]
        source: serde_json::Error,
    },

    /// 策略初始化失败
    #[error("策略 '{strategy_name}' 初始化失败: {reason}")]
    InitializationFailed {
        strategy_name: String,
        reason: String,
    },

    /// 依赖的服务验证失败
    #[error("依赖的服务验证失败: {service_name} - {reason}")]
    ServiceValidationFailed {
        service_name: String,
        reason: String,
    },

    /// 策略构建器错误
    #[error("策略构建器错误: {reason}")]
    BuilderError {
        reason: String,
    },

    /// 配置验证失败
    #[error("配置验证失败: {field} - {reason}")]
    ConfigValidationFailed {
        field: String,
        reason: String,
    },
}

impl FactoryError {
    /// 创建不支持策略类型的错误
    pub fn unsupported_type(strategy_type: StrategyType) -> Self {
        Self::UnsupportedType {
            strategy_type,
            supported_types: StrategyType::all(),
        }
    }

    /// 创建初始化失败的错误
    pub fn initialization_failed(strategy_name: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::InitializationFailed {
            strategy_name: strategy_name.into(),
            reason: reason.into(),
        }
    }

    /// 创建服务验证失败的错误
    pub fn service_validation_failed(service_name: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ServiceValidationFailed {
            service_name: service_name.into(),
            reason: reason.into(),
        }
    }

    /// 创建构建器错误
    pub fn builder_error(reason: impl Into<String>) -> Self {
        Self::BuilderError {
            reason: reason.into(),
        }
    }

    /// 创建配置验证失败的错误
    pub fn config_validation_failed(field: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ConfigValidationFailed {
            field: field.into(),
            reason: reason.into(),
        }
    }

    /// 获取错误代码（用于程序化处理）
    pub fn error_code(&self) -> &'static str {
        match self {
            Self::UnsupportedType { .. } => "FACTORY_UNSUPPORTED_TYPE",
            Self::InvalidConfig { .. } => "FACTORY_INVALID_CONFIG",
            Self::InitializationFailed { .. } => "FACTORY_INITIALIZATION_FAILED",
            Self::ServiceValidationFailed { .. } => "FACTORY_SERVICE_VALIDATION_FAILED",
            Self::BuilderError { .. } => "FACTORY_BUILDER_ERROR",
            Self::ConfigValidationFailed { .. } => "FACTORY_CONFIG_VALIDATION_FAILED",
        }
    }

    /// 获取错误建议
    pub fn suggestion(&self) -> Option<&'static str> {
        match self {
            Self::UnsupportedType { .. } => Some("请检查策略类型是否正确，或联系开发团队添加新的策略类型支持"),
            Self::InvalidConfig { .. } => Some("请检查配置格式是否正确，参考文档中的配置示例"),
            Self::InitializationFailed { .. } => Some("请检查策略配置和依赖服务是否正确设置"),
            Self::ServiceValidationFailed { .. } => Some("请检查相关服务是否正确初始化和配置"),
            Self::BuilderError { .. } => Some("请检查构建器的使用方式是否正确"),
            Self::ConfigValidationFailed { .. } => Some("请检查配置字段的值是否在有效范围内"),
        }
    }
}

impl From<FactoryError> for SigmaXError {
    fn from(err: FactoryError) -> Self {
        SigmaXError::strategy_error(err.to_string(), uuid::Uuid::new_v4())
    }
}
