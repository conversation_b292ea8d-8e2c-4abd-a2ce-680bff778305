//! 执行相关接口定义
//!
//! 基于核心设计原则的统一执行接口：
//! - 面向接口设计：清晰的抽象边界
//! - 单一职责：专注执行逻辑
//! - 简洁性：最小化接口复杂度

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, EngineType, EngineId, EngineStatistics, UnifiedEngineConfig, SigmaXResult};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// ============================================================================
// 统一执行引擎接口 - 替代重复的接口定义
// ============================================================================

/// 统一执行引擎接口
///
/// 设计原则：
/// - 合并 ExecutionEngine 和 TradingEngine 的功能
/// - 提供完整的引擎生命周期管理
/// - 支持依赖注入和可测试性
#[async_trait]
pub trait ExecutionEngine: Send + Sync {
    // ========================================================================
    // 核心执行功能
    // ========================================================================

    /// 执行订单 - 核心业务功能
    async fn execute_order(&self, order: &Order) -> ExecutionResult;

    /// 取消订单
    async fn cancel_order(&self, order_id: &Uuid) -> ExecutionResult;

    // ========================================================================
    // 引擎管理功能
    // ========================================================================

    /// 获取引擎ID
    fn id(&self) -> EngineId;

    /// 获取引擎类型
    fn engine_type(&self) -> EngineType;

    /// 获取引擎状态
    async fn get_status(&self) -> EngineRunStatus;

    /// 启动引擎
    async fn start(&self) -> SigmaXResult<()>;

    /// 停止引擎
    async fn stop(&self) -> SigmaXResult<()>;

    /// 暂停引擎
    async fn pause(&self) -> SigmaXResult<()>;

    /// 恢复引擎
    async fn resume(&self) -> SigmaXResult<()>;

    // ========================================================================
    // 配置和监控功能
    // ========================================================================

    /// 获取引擎统计信息
    async fn get_statistics(&self) -> SigmaXResult<EngineStatistics>;

    /// 获取性能指标
    async fn get_performance_metrics(&self) -> SigmaXResult<PerformanceMetrics>;

    /// 类型转换支持
    fn as_any(&self) -> &dyn std::any::Any;
}

// ============================================================================
// 执行结果和状态定义
// ============================================================================

/// 执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    /// 是否成功
    pub success: bool,
    /// 订单ID
    pub order_id: Option<Uuid>,
    /// 执行ID
    pub execution_id: Uuid,
    /// 执行状态
    pub status: ExecutionStatus,
    /// 执行时间戳
    pub timestamp: DateTime<Utc>,
    /// 错误信息（如果失败）
    pub error_message: Option<String>,
    /// 执行延迟（毫秒）
    pub latency_ms: Option<u64>,
    /// 额外的执行信息
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStatus {
    /// 已提交
    Submitted,
    /// 执行中
    Executing,
    /// 已完成
    Completed,
    /// 已取消
    Cancelled,
    /// 失败
    Failed,
    /// 部分成交
    PartiallyFilled,
}

/// 引擎运行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EngineRunStatus {
    /// 已停止
    Stopped,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 暂停中
    Paused,
    /// 停止中
    Stopping,
    /// 错误状态
    Error(String),
    /// 维护中
    Maintenance,
}

// ============================================================================
// 引擎管理接口
// ============================================================================

/// 引擎管理器接口 - 简化版
#[async_trait]
pub trait EngineManager: Send + Sync {
    /// 创建引擎
    async fn create_engine(&self, config: &UnifiedEngineConfig) -> SigmaXResult<Box<dyn ExecutionEngine>>;

    /// 获取引擎
    async fn get_engine(&self, engine_id: &EngineId) -> Option<&dyn ExecutionEngine>;

    /// 移除引擎
    async fn remove_engine(&self, engine_id: &EngineId) -> SigmaXResult<()>;

    /// 列出所有引擎
    async fn list_engines(&self) -> Vec<EngineInfo>;

    /// 健康检查
    async fn health_check(&self) -> HealthCheckResult;
}

// ============================================================================
// 支持数据结构定义
// ============================================================================

// 注意：EngineConfig 已迁移到 sigmax_core::UnifiedEngineConfig
// 这里保留类型别名以便兼容
pub type EngineConfig = UnifiedEngineConfig;

/// 资源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceConfig {
    pub max_concurrent_orders: usize,
    pub timeout_ms: u64,
    pub retry_attempts: u32,
    pub memory_limit_mb: Option<usize>,
}

/// 引擎信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineInfo {
    pub engine_id: Uuid,
    pub engine_type: EngineType,
    pub name: String,
    pub status: EngineRunStatus,
    pub created_at: DateTime<Utc>,
    pub last_active: DateTime<Utc>,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub orders_executed: u64,
    pub average_latency_ms: f64,
    pub success_rate: f64,
    pub error_count: u64,
    pub last_updated: DateTime<Utc>,
}

/// 健康检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    pub healthy: bool,
    pub timestamp: DateTime<Utc>,
    pub details: HashMap<String, String>,
    pub engine_count: usize,
    pub error_count: u64,
}

// ============================================================================
// 错误类型
// ============================================================================

/// 执行错误
#[derive(Debug, thiserror::Error)]
pub enum ExecutionError {
    #[error("Order execution failed: {reason}")]
    ExecutionFailed { reason: String },
    
    #[error("Order not found: {order_id}")]
    OrderNotFound { order_id: Uuid },
    
    #[error("Engine not available: {engine_type}")]
    EngineNotAvailable { engine_type: String },
    
    #[error("Execution timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
    
    #[error("Invalid order state: {current_state}")]
    InvalidOrderState { current_state: String },
    
    #[error("Resource limit exceeded: {resource}")]
    ResourceLimitExceeded { resource: String },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("Network error: {message}")]
    Network { message: String },
    
    #[error("Exchange error: {exchange} - {message}")]
    Exchange { exchange: String, message: String },
}