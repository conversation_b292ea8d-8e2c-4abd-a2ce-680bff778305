//! 服务模块错误定义
//!
//! 这个模块现在使用新的错误处理系统。
//! 基于 SigmaX 统一错误类型。

use sigmax_core::{SigmaXError, SigmaXResult};
use std::collections::HashMap;

// 重新导出核心错误类型
pub use sigmax_core::{
    SigmaXError as ServiceError,
    SigmaXResult as ServiceResult,
};

/// 创建缓存错误的便利函数
pub fn cache_error(message: &str, cache_type: &str, operation: &str) -> SigmaXError {
    SigmaXError::Cache(sigmax_core::errors::infrastructure::CacheError::OperationFailed {
        operation: operation.to_string(),
        key: "unknown".to_string(),
        message: format!("{} (cache_type: {})", message, cache_type),
    })
}

/// 创建指标收集错误的便利函数
pub fn metrics_error(message: &str, component: &str) -> SigmaXError {
    SigmaXError::internal_error(format!("Metrics error in {}: {}", component, message))
}

/// 创建配置错误的便利函数
pub fn config_error(message: &str, config_key: Option<&str>, config_file: Option<&str>) -> SigmaXError {
    let full_message = match (config_key, config_file) {
        (Some(key), Some(file)) => format!("{} (key: {}, file: {})", message, key, file),
        (Some(key), None) => format!("{} (key: {})", message, key),
        (None, Some(file)) => format!("{} (file: {})", message, file),
        (None, None) => message.to_string(),
    };
    SigmaXError::config_error(full_message)
}

/// 创建网络错误的便利函数
pub fn network_error(message: &str, endpoint: &str) -> SigmaXError {
    SigmaXError::Network(sigmax_core::errors::infrastructure::NetworkError::Timeout {
        endpoint: endpoint.to_string(),
        timeout_ms: 30000,
    })
}

/// 创建数据库错误的便利函数
pub fn database_error(message: &str) -> SigmaXError {
    SigmaXError::database_error(message)
}

/// 创建验证错误的便利函数
pub fn validation_error(field: &str, message: &str) -> SigmaXError {
    SigmaXError::validation_error(field, message)
}
