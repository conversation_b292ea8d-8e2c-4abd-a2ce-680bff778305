//! Mock 订单仓储实现
//!
//! 用于单元测试和集成测试的内存版本订单仓储

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use async_trait::async_trait;
use chrono::{DateTime, Utc};

use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, TradingPair, ExchangeId,
    Quantity, Price, Amount, SigmaXResult, SigmaXError
};

use super::traits::enhanced_order_repository::{
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort,
    OrderStatistics, OrderSortBy, SortDirection
};

/// Mock 订单仓储实现
pub struct MockOrderRepository {
    /// 内存存储
    orders: Arc<RwLock<HashMap<OrderId, Order>>>,
    /// 模拟延迟（毫秒）
    simulated_delay_ms: Option<u64>,
    /// 是否模拟错误
    simulate_errors: bool,
    /// 错误概率（0.0-1.0）
    error_probability: f64,
}

impl MockOrderRepository {
    /// 创建新的 Mock 仓储
    pub fn new() -> Self {
        Self {
            orders: Arc::new(RwLock::new(HashMap::new())),
            simulated_delay_ms: None,
            simulate_errors: false,
            error_probability: 0.0,
        }
    }

    /// 设置模拟延迟
    pub fn with_simulated_delay(mut self, delay_ms: u64) -> Self {
        self.simulated_delay_ms = Some(delay_ms);
        self
    }

    /// 启用错误模拟
    pub fn with_error_simulation(mut self, probability: f64) -> Self {
        self.simulate_errors = true;
        self.error_probability = probability.clamp(0.0, 1.0);
        self
    }

    /// 插入测试数据
    pub async fn insert_test_data(&self, orders: Vec<Order>) -> SigmaXResult<()> {
        let mut store = self.orders.write().await;
        for order in orders {
            store.insert(order.id, order);
        }
        Ok(())
    }

    /// 清空所有数据
    pub async fn clear(&self) -> SigmaXResult<()> {
        let mut store = self.orders.write().await;
        store.clear();
        Ok(())
    }

    /// 获取当前存储的订单数量
    pub async fn count(&self) -> usize {
        let store = self.orders.read().await;
        store.len()
    }

    /// 模拟延迟
    async fn simulate_delay(&self) {
        if let Some(delay_ms) = self.simulated_delay_ms {
            tokio::time::sleep(tokio::time::Duration::from_millis(delay_ms)).await;
        }
    }

    /// 模拟错误
    fn simulate_error(&self) -> SigmaXResult<()> {
        if self.simulate_errors {
            // 简化的错误模拟逻辑，避免依赖 rand crate
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{Hash, Hasher};
            use std::time::{SystemTime, UNIX_EPOCH};

            let mut hasher = DefaultHasher::new();
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
            let hash = hasher.finish();

            // 使用哈希值模拟随机性
            if (hash % 100) as f64 / 100.0 < self.error_probability {
                return Err(SigmaXError::database_error("模拟数据库错误"));
            }
        }
        Ok(())
    }

    /// 应用查询过滤器
    fn apply_filter(&self, orders: &[Order], filter: &OrderQueryFilter) -> Vec<Order> {
        orders.iter()
            .filter(|order| {
                // 策略ID过滤
                if let Some(ref strategy_id) = filter.strategy_id {
                    if order.strategy_id.as_ref() != Some(strategy_id) {
                        return false;
                    }
                }

                // 交易所ID过滤
                if let Some(ref exchange_id) = filter.exchange_id {
                    if &order.exchange_id != exchange_id {
                        return false;
                    }
                }

                // 交易对过滤
                if let Some(ref trading_pair) = filter.trading_pair {
                    if &order.trading_pair != trading_pair {
                        return false;
                    }
                }

                // 状态过滤
                if let Some(ref status) = filter.status {
                    if &order.status != status {
                        return false;
                    }
                }

                // 订单方向过滤
                if let Some(ref side) = filter.side {
                    if &order.side != side {
                        return false;
                    }
                }

                // 订单类型过滤
                if let Some(ref order_type) = filter.order_type {
                    if &order.order_type != order_type {
                        return false;
                    }
                }

                // 时间范围过滤
                if let Some(created_after) = filter.created_after {
                    if order.created_at < created_after {
                        return false;
                    }
                }

                if let Some(created_before) = filter.created_before {
                    if order.created_at > created_before {
                        return false;
                    }
                }

                // 数量范围过滤
                if let Some(min_quantity) = filter.min_quantity {
                    if order.quantity < min_quantity {
                        return false;
                    }
                }

                if let Some(max_quantity) = filter.max_quantity {
                    if order.quantity > max_quantity {
                        return false;
                    }
                }

                // 价格范围过滤
                if let Some(min_price) = filter.min_price {
                    if let Some(price) = order.price {
                        if price < min_price {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }

                if let Some(max_price) = filter.max_price {
                    if let Some(price) = order.price {
                        if price > max_price {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }

                true
            })
            .cloned()
            .collect()
    }

    /// 应用排序
    fn apply_sort(&self, mut orders: Vec<Order>, sort: Option<&OrderSort>) -> Vec<Order> {
        if let Some(sort) = sort {
            orders.sort_by(|a, b| {
                let comparison = match sort.sort_by {
                    OrderSortBy::CreatedAt => a.created_at.cmp(&b.created_at),
                    OrderSortBy::UpdatedAt => a.updated_at.cmp(&b.updated_at),
                    OrderSortBy::Quantity => a.quantity.cmp(&b.quantity),
                    OrderSortBy::Price => {
                        match (a.price, b.price) {
                            (Some(price_a), Some(price_b)) => price_a.cmp(&price_b),
                            (Some(_), None) => std::cmp::Ordering::Greater,
                            (None, Some(_)) => std::cmp::Ordering::Less,
                            (None, None) => std::cmp::Ordering::Equal,
                        }
                    }
                    OrderSortBy::Status => format!("{:?}", a.status).cmp(&format!("{:?}", b.status)),
                };

                match sort.direction {
                    SortDirection::Asc => comparison,
                    SortDirection::Desc => comparison.reverse(),
                }
            });
        }
        orders
    }

    /// 应用分页
    fn apply_pagination(&self, orders: Vec<Order>, pagination: Option<&Pagination>) -> Vec<Order> {
        if let Some(pagination) = pagination {
            orders.into_iter()
                .skip(pagination.offset)
                .take(pagination.limit)
                .collect()
        } else {
            orders
        }
    }
}

impl Default for MockOrderRepository {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl EnhancedOrderRepository for MockOrderRepository {
    async fn save(&self, order: &Order) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        // 验证订单
        order.validate_complete()
            .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;

        let mut orders = self.orders.write().await;
        orders.insert(order.id, order.clone());
        Ok(())
    }

    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        Ok(orders.get(&id).cloned())
    }

    async fn update(&self, order: &Order) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        // 验证订单
        order.validate_complete()
            .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;

        let mut orders = self.orders.write().await;
        if orders.contains_key(&order.id) {
            orders.insert(order.id, order.clone());
            Ok(())
        } else {
            Err(SigmaXError::NotFound(format!("订单不存在: {}", order.id)))
        }
    }

    async fn delete(&self, id: OrderId) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let mut orders = self.orders.write().await;
        if orders.remove(&id).is_some() {
            Ok(())
        } else {
            Err(SigmaXError::NotFound(format!("订单不存在: {}", id)))
        }
    }

    async fn exists(&self, id: OrderId) -> SigmaXResult<bool> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        Ok(orders.contains_key(&id))
    }

    async fn save_batch(&self, orders: &[Order]) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let mut store = self.orders.write().await;
        for order in orders {
            // 验证每个订单
            order.validate_complete()
                .map_err(|e| SigmaXError::Internal(format!("订单验证失败: {}", e)))?;
            store.insert(order.id, order.clone());
        }
        Ok(())
    }

    async fn update_status_batch(&self, ids: &[OrderId], status: OrderStatus) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let mut store = self.orders.write().await;
        for id in ids {
            if let Some(order) = store.get_mut(id) {
                order.update_status(status);
            }
        }
        Ok(())
    }

    async fn delete_batch(&self, ids: &[OrderId]) -> SigmaXResult<()> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let mut store = self.orders.write().await;
        for id in ids {
            store.remove(id);
        }
        Ok(())
    }

    async fn find_by_filter(
        &self,
        filter: &OrderQueryFilter,
        pagination: Option<&Pagination>,
        sort: Option<&OrderSort>
    ) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        let all_orders: Vec<Order> = orders.values().cloned().collect();
        
        let filtered = self.apply_filter(&all_orders, filter);
        let sorted = self.apply_sort(filtered, sort);
        let paginated = self.apply_pagination(sorted, pagination);
        
        Ok(paginated)
    }

    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            status: Some(status),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            strategy_id: Some(strategy_id),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            trading_pair: Some(trading_pair.clone()),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_by_exchange(&self, exchange_id: &ExchangeId) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            exchange_id: Some(exchange_id.clone()),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_pending_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.find_by_status(OrderStatus::Pending).await
    }

    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        let active_orders: Vec<Order> = orders.values()
            .filter(|order| matches!(order.status, OrderStatus::Pending | OrderStatus::PartiallyFilled))
            .cloned()
            .collect();
        
        Ok(active_orders)
    }

    async fn find_by_timerange(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>
    ) -> SigmaXResult<Vec<Order>> {
        let filter = OrderQueryFilter {
            created_after: Some(start),
            created_before: Some(end),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    // 其他方法的实现...
    async fn count_by_status(&self, status: OrderStatus) -> SigmaXResult<u64> {
        let orders = self.find_by_status(status).await?;
        Ok(orders.len() as u64)
    }

    async fn count_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<u64> {
        let orders = self.find_by_strategy(strategy_id).await?;
        Ok(orders.len() as u64)
    }

    async fn get_total_volume_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Quantity> {
        let orders = self.find_by_strategy(strategy_id).await?;
        let total_volume = orders.iter()
            .map(|order| order.quantity)
            .fold(Quantity::ZERO, |acc, qty| acc + qty);
        Ok(total_volume)
    }

    async fn get_total_value_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Amount> {
        let orders = self.find_by_strategy(strategy_id).await?;
        let total_value = orders.iter()
            .filter_map(|order| order.price.map(|price| Amount::from(order.quantity * price)))
            .fold(Amount::ZERO, |acc, value| acc + value);
        Ok(total_value)
    }

    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = if let Some(filter) = filter {
            self.find_by_filter(filter, None, None).await?
        } else {
            let store = self.orders.read().await;
            store.values().cloned().collect()
        };

        let total_count = orders.len() as u64;
        let pending_count = orders.iter().filter(|o| o.status == OrderStatus::Pending).count() as u64;
        let filled_count = orders.iter().filter(|o| o.status == OrderStatus::Filled).count() as u64;
        let cancelled_count = orders.iter().filter(|o| o.status == OrderStatus::Cancelled).count() as u64;

        let total_volume = orders.iter()
            .map(|order| order.quantity)
            .fold(Quantity::ZERO, |acc, qty| acc + qty);

        let total_value = orders.iter()
            .filter_map(|order| order.price.map(|price| Amount::from(order.quantity * price)))
            .fold(Amount::ZERO, |acc, value| acc + value);

        let average_order_size = if total_count > 0 {
            total_volume / rust_decimal::Decimal::from(total_count)
        } else {
            Quantity::ZERO
        };

        Ok(OrderStatistics {
            total_count,
            pending_count,
            filled_count,
            cancelled_count,
            total_volume,
            total_value,
            average_order_size,
        })
    }

    async fn get_strategy_statistics(&self, strategy_id: StrategyId) -> SigmaXResult<OrderStatistics> {
        let filter = OrderQueryFilter {
            strategy_id: Some(strategy_id),
            ..Default::default()
        };
        self.get_statistics(Some(&filter)).await
    }

    // 简化实现其他方法...
    async fn find_orders_for_risk_check(&self) -> SigmaXResult<Vec<Order>> {
        self.find_pending_orders().await
    }

    async fn find_timeout_pending_orders(&self, timeout_minutes: u32) -> SigmaXResult<Vec<Order>> {
        let timeout_duration = chrono::Duration::minutes(timeout_minutes as i64);
        let cutoff_time = Utc::now() - timeout_duration;
        
        let filter = OrderQueryFilter {
            status: Some(OrderStatus::Pending),
            created_before: Some(cutoff_time),
            ..Default::default()
        };
        self.find_by_filter(&filter, None, None).await
    }

    async fn find_large_orders(&self, min_value: Amount) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        let large_orders: Vec<Order> = orders.values()
            .filter(|order| {
                if let Some(price) = order.price {
                    Amount::from(order.quantity * price) >= min_value
                } else {
                    false
                }
            })
            .cloned()
            .collect();
        
        Ok(large_orders)
    }

    async fn find_abnormal_orders(
        &self,
        trading_pair: &TradingPair,
        market_price: Price,
        deviation_percent: f64
    ) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        use rust_decimal::prelude::FromPrimitive;
        let max_deviation = market_price * rust_decimal::Decimal::from_f64(deviation_percent / 100.0).unwrap();
        let min_price = market_price - max_deviation;
        let max_price = market_price + max_deviation;

        let orders = self.orders.read().await;
        let abnormal_orders: Vec<Order> = orders.values()
            .filter(|order| {
                &order.trading_pair == trading_pair &&
                order.price.map_or(false, |price| price < min_price || price > max_price)
            })
            .cloned()
            .collect();
        
        Ok(abnormal_orders)
    }

    async fn get_recent_orders(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        let sort = OrderSort {
            sort_by: OrderSortBy::CreatedAt,
            direction: SortDirection::Desc,
        };
        let pagination = Pagination {
            offset: 0,
            limit,
        };
        
        self.find_by_filter(&OrderQueryFilter::default(), Some(&pagination), Some(&sort)).await
    }

    async fn get_orders_by_popular_pairs(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        // 简化实现：返回最近的订单
        self.get_recent_orders(limit).await
    }

    async fn preload_orders_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        self.simulate_error()?;

        let orders = self.orders.read().await;
        let result: Vec<Order> = order_ids.iter()
            .filter_map(|id| orders.get(id).cloned())
            .collect();
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sigmax_core::{TradingPair, OrderSide, OrderType, ExchangeId};
    use std::str::FromStr;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_mock_repository_basic_operations() {
        let repo = MockOrderRepository::new();
        
        let order = Order::new(
            ExchangeId::from("test_exchange"),
            TradingPair::new("BTC".to_string(), "USDT".to_string()),
            OrderSide::Buy,
            OrderType::Limit,
            Quantity::from_str("1.0").unwrap(),
            Some(Price::from_str("50000.0").unwrap()),
        );

        // 测试保存
        repo.save(&order).await.unwrap();
        
        // 测试查找
        let found_order = repo.find_by_id(order.id).await.unwrap();
        assert!(found_order.is_some());
        assert_eq!(found_order.unwrap().id, order.id);
        
        // 测试存在性检查
        assert!(repo.exists(order.id).await.unwrap());
        
        // 测试删除
        repo.delete(order.id).await.unwrap();
        assert!(!repo.exists(order.id).await.unwrap());
    }

    #[tokio::test]
    async fn test_mock_repository_filtering() {
        let repo = MockOrderRepository::new();
        
        let order1 = Order::new(
            ExchangeId::from("exchange1"),
            TradingPair::new("BTC".to_string(), "USDT".to_string()),
            OrderSide::Buy,
            OrderType::Limit,
            Quantity::from_str("1.0").unwrap(),
            Some(Price::from_str("50000.0").unwrap()),
        );
        
        let order2 = Order::new(
            ExchangeId::from("exchange2"),
            TradingPair::new("ETH".to_string(), "USDT".to_string()),
            OrderSide::Sell,
            OrderType::Market,
            Quantity::from_str("2.0").unwrap(),
            None,
        );

        repo.save(&order1).await.unwrap();
        repo.save(&order2).await.unwrap();

        // 测试按交易对过滤
        let btc_orders = repo.find_by_trading_pair(&TradingPair::new("BTC".to_string(), "USDT".to_string())).await.unwrap();
        assert_eq!(btc_orders.len(), 1);
        assert_eq!(btc_orders[0].id, order1.id);

        // 测试按状态过滤
        let pending_orders = repo.find_by_status(OrderStatus::Pending).await.unwrap();
        assert_eq!(pending_orders.len(), 2);
    }
}
