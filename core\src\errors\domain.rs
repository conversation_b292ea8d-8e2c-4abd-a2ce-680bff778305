//! 领域错误定义
//! 
//! 遵循高内聚原则：每个领域的错误类型内聚在一起
//! 遵循低耦合原则：领域错误之间相互独立

use thiserror::Error;
use std::collections::HashMap;

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ErrorSeverity {
    /// 信息性 - 不影响业务流程
    Info,
    /// 警告 - 需要关注但可继续
    Warning,
    /// 错误 - 影响当前操作
    Error,
    /// 严重 - 可能影响系统稳定性
    Critical,
}

/// 错误类别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorCategory {
    /// 业务逻辑错误
    Business,
    /// 技术基础设施错误
    Infrastructure,
    /// 外部依赖错误
    External,
    /// 输入验证错误
    Validation,
    /// 技术错误（序列化、解析等）
    Technical,
}

/// 核心错误特征 - 面向接口设计
pub trait DomainError: std::error::Error + Send + Sync + 'static {
    /// 错误类别
    fn category(&self) -> ErrorCategory;
    
    /// 错误严重程度
    fn severity(&self) -> ErrorSeverity;
    
    /// 错误代码 - 用于程序化处理
    fn error_code(&self) -> &'static str;
    
    /// 是否可重试
    fn is_retryable(&self) -> bool {
        matches!(self.category(), ErrorCategory::External | ErrorCategory::Infrastructure)
            && !matches!(self.severity(), ErrorSeverity::Critical)
    }
    
    /// 用户友好消息
    fn user_message(&self) -> String {
        self.to_string()
    }
    
    /// 错误元数据
    fn metadata(&self) -> HashMap<String, String> {
        HashMap::new()
    }
}

/// 交易相关错误 - 高内聚的业务错误
#[derive(Error, Debug, Clone)]
pub enum TradingError {
    #[error("Invalid order: {message}")]
    InvalidOrder { 
        message: String,
        order_id: Option<uuid::Uuid>,
    },
    
    #[error("Insufficient balance: required {required}, available {available}")]
    InsufficientBalance { 
        required: rust_decimal::Decimal,
        available: rust_decimal::Decimal,
        currency: String,
    },
    
    #[error("Order not found: {order_id}")]
    OrderNotFound { 
        order_id: uuid::Uuid,
    },
    
    #[error("Invalid trading pair: {pair}")]
    InvalidTradingPair { 
        pair: String,
    },
    
    #[error("Market closed for pair: {pair}")]
    MarketClosed { 
        pair: String,
    },
}

impl DomainError for TradingError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Business
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            TradingError::InvalidOrder { .. } => ErrorSeverity::Error,
            TradingError::InsufficientBalance { .. } => ErrorSeverity::Warning,
            TradingError::OrderNotFound { .. } => ErrorSeverity::Warning,
            TradingError::InvalidTradingPair { .. } => ErrorSeverity::Error,
            TradingError::MarketClosed { .. } => ErrorSeverity::Info,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            TradingError::InvalidOrder { .. } => "TRADING_INVALID_ORDER",
            TradingError::InsufficientBalance { .. } => "TRADING_INSUFFICIENT_BALANCE",
            TradingError::OrderNotFound { .. } => "TRADING_ORDER_NOT_FOUND",
            TradingError::InvalidTradingPair { .. } => "TRADING_INVALID_PAIR",
            TradingError::MarketClosed { .. } => "TRADING_MARKET_CLOSED",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            TradingError::InvalidOrder { order_id, .. } => {
                if let Some(id) = order_id {
                    meta.insert("order_id".to_string(), id.to_string());
                }
            },
            TradingError::InsufficientBalance { required, available, currency } => {
                meta.insert("required_amount".to_string(), required.to_string());
                meta.insert("available_amount".to_string(), available.to_string());
                meta.insert("currency".to_string(), currency.clone());
            },
            TradingError::OrderNotFound { order_id } => {
                meta.insert("order_id".to_string(), order_id.to_string());
            },
            TradingError::InvalidTradingPair { pair } => {
                meta.insert("trading_pair".to_string(), pair.clone());
            },
            TradingError::MarketClosed { pair } => {
                meta.insert("trading_pair".to_string(), pair.clone());
            },
        }
        meta
    }
}

/// 策略相关错误 - 高内聚的业务错误
#[derive(Error, Debug, Clone)]
pub enum StrategyError {
    #[error("Strategy not found: {strategy_id}")]
    NotFound { 
        strategy_id: uuid::Uuid,
    },
    
    #[error("Strategy configuration invalid: {message}")]
    InvalidConfiguration { 
        message: String,
        strategy_type: String,
    },
    
    #[error("Strategy execution failed: {message}")]
    ExecutionFailed { 
        message: String,
        strategy_id: uuid::Uuid,
    },
    
    #[error("Strategy state inconsistent: expected {expected}, got {actual}")]
    StateInconsistent { 
        expected: String,
        actual: String,
        strategy_id: uuid::Uuid,
    },
}

impl DomainError for StrategyError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Business
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            StrategyError::NotFound { .. } => ErrorSeverity::Warning,
            StrategyError::InvalidConfiguration { .. } => ErrorSeverity::Error,
            StrategyError::ExecutionFailed { .. } => ErrorSeverity::Error,
            StrategyError::StateInconsistent { .. } => ErrorSeverity::Critical,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            StrategyError::NotFound { .. } => "STRATEGY_NOT_FOUND",
            StrategyError::InvalidConfiguration { .. } => "STRATEGY_INVALID_CONFIG",
            StrategyError::ExecutionFailed { .. } => "STRATEGY_EXECUTION_FAILED",
            StrategyError::StateInconsistent { .. } => "STRATEGY_STATE_INCONSISTENT",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            StrategyError::NotFound { strategy_id } => {
                meta.insert("strategy_id".to_string(), strategy_id.to_string());
            },
            StrategyError::InvalidConfiguration { strategy_type, .. } => {
                meta.insert("strategy_type".to_string(), strategy_type.clone());
            },
            StrategyError::ExecutionFailed { strategy_id, .. } => {
                meta.insert("strategy_id".to_string(), strategy_id.to_string());
            },
            StrategyError::StateInconsistent { expected, actual, strategy_id } => {
                meta.insert("expected_state".to_string(), expected.clone());
                meta.insert("actual_state".to_string(), actual.clone());
                meta.insert("strategy_id".to_string(), strategy_id.to_string());
            },
        }
        meta
    }
}

/// 风险管理错误 - 高内聚的业务错误
#[derive(Error, Debug, Clone)]
pub enum RiskError {
    #[error("Risk limit exceeded: {limit_type} limit {limit} exceeded with value {current}")]
    LimitExceeded { 
        limit_type: String,
        limit: rust_decimal::Decimal,
        current: rust_decimal::Decimal,
    },
    
    #[error("Risk check failed: {message}")]
    CheckFailed { 
        message: String,
        check_type: String,
    },
    
    #[error("Risk configuration invalid: {message}")]
    InvalidConfiguration { 
        message: String,
    },
}

impl DomainError for RiskError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Business
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            RiskError::LimitExceeded { .. } => ErrorSeverity::Critical,
            RiskError::CheckFailed { .. } => ErrorSeverity::Error,
            RiskError::InvalidConfiguration { .. } => ErrorSeverity::Error,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            RiskError::LimitExceeded { .. } => "RISK_LIMIT_EXCEEDED",
            RiskError::CheckFailed { .. } => "RISK_CHECK_FAILED",
            RiskError::InvalidConfiguration { .. } => "RISK_INVALID_CONFIG",
        }
    }
    
    fn is_retryable(&self) -> bool {
        false // 风险错误通常不应重试
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            RiskError::LimitExceeded { limit_type, limit, current } => {
                meta.insert("limit_type".to_string(), limit_type.clone());
                meta.insert("limit_value".to_string(), limit.to_string());
                meta.insert("current_value".to_string(), current.to_string());
            },
            RiskError::CheckFailed { check_type, .. } => {
                meta.insert("check_type".to_string(), check_type.clone());
            },
            _ => {},
        }
        meta
    }
}

/// 验证错误 - 高内聚的验证错误
#[derive(Error, Debug, Clone)]
pub enum ValidationError {
    #[error("Field validation failed: {field} - {message}")]
    FieldValidation { 
        field: String,
        message: String,
        value: Option<String>,
    },
    
    #[error("Format validation failed: expected {expected_format}, got {actual_value}")]
    FormatValidation { 
        expected_format: String,
        actual_value: String,
    },
    
    #[error("Range validation failed: {field} must be between {min} and {max}, got {value}")]
    RangeValidation { 
        field: String,
        min: String,
        max: String,
        value: String,
    },
}

impl DomainError for ValidationError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Validation
    }
    
    fn severity(&self) -> ErrorSeverity {
        ErrorSeverity::Error
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            ValidationError::FieldValidation { .. } => "VALIDATION_FIELD_ERROR",
            ValidationError::FormatValidation { .. } => "VALIDATION_FORMAT_ERROR",
            ValidationError::RangeValidation { .. } => "VALIDATION_RANGE_ERROR",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            ValidationError::FieldValidation { field, value, .. } => {
                meta.insert("field_name".to_string(), field.clone());
                if let Some(v) = value {
                    meta.insert("field_value".to_string(), v.clone());
                }
            },
            ValidationError::FormatValidation { expected_format, actual_value } => {
                meta.insert("expected_format".to_string(), expected_format.clone());
                meta.insert("actual_value".to_string(), actual_value.clone());
            },
            ValidationError::RangeValidation { field, min, max, value } => {
                meta.insert("field_name".to_string(), field.clone());
                meta.insert("min_value".to_string(), min.clone());
                meta.insert("max_value".to_string(), max.clone());
                meta.insert("actual_value".to_string(), value.clone());
            },
        }
        meta
    }
}
