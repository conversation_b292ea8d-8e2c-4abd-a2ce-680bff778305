//! 错误类型转换
//!
//! 提供从现有错误类型到统一错误类型的转换

use super::unified::{UnifiedError, BusinessError, TechnicalError, ExternalError, ValidationError};
use std::collections::HashMap;

/// 从现有的SigmaXError转换为UnifiedError
impl From<crate::errors::SigmaXError> for UnifiedError {
    fn from(err: crate::errors::SigmaXError) -> Self {
        let mut details = HashMap::new();
        
        match err {
            crate::errors::SigmaXError::Exchange(msg) => {
                UnifiedError::Business(BusinessError::Exchange {
                    message: msg,
                    exchange_id: None,
                    symbol: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Order(msg) => {
                UnifiedError::Business(BusinessError::Order {
                    message: msg,
                    order_id: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Strategy(msg) => {
                UnifiedError::Business(BusinessError::Strategy {
                    message: msg,
                    strategy_id: None,
                    strategy_type: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Risk(msg) => {
                UnifiedError::Business(BusinessError::Risk {
                    message: msg,
                    risk_type: "unknown".to_string(),
                    threshold: None,
                    current_value: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Data(msg) => {
                details.insert("data_type".to_string(), "unknown".to_string());
                UnifiedError::Technical(TechnicalError::Internal {
                    message: msg,
                    component: "data_processing".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::Network(msg) => {
                UnifiedError::Technical(TechnicalError::Network {
                    message: msg,
                    endpoint: None,
                    status_code: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Config(msg) => {
                UnifiedError::Technical(TechnicalError::Configuration {
                    message: msg,
                    config_key: None,
                    config_file: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Database(msg) => {
                UnifiedError::Technical(TechnicalError::Database {
                    message: msg,
                    operation: "unknown".to_string(),
                    table: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Serialization(msg) => {
                UnifiedError::Technical(TechnicalError::Serialization {
                    message: msg,
                    data_type: "unknown".to_string(),
                    format: "unknown".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::InvalidParameter(msg) => {
                UnifiedError::Validation(ValidationError::Field {
                    field: "unknown".to_string(),
                    message: msg,
                    value: None,
                    constraint: "parameter_validation".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::NotFound(msg) => {
                UnifiedError::Business(BusinessError::Order {
                    message: format!("Resource not found: {}", msg),
                    order_id: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Unauthorized => {
                UnifiedError::Business(BusinessError::PermissionDenied {
                    message: "Unauthorized access".to_string(),
                    required_permission: "unknown".to_string(),
                    user_id: None,
                    details,
                })
            },
            crate::errors::SigmaXError::RateLimit => {
                UnifiedError::External(ExternalError::RateLimit {
                    message: "Rate limit exceeded".to_string(),
                    service: "unknown".to_string(),
                    limit: None,
                    reset_time: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Internal(msg) => {
                UnifiedError::Technical(TechnicalError::Internal {
                    message: msg,
                    component: "unknown".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::RiskManagement(msg) => {
                UnifiedError::Business(BusinessError::Risk {
                    message: msg,
                    risk_type: "risk_management".to_string(),
                    threshold: None,
                    current_value: None,
                    details,
                })
            },
            crate::errors::SigmaXError::InvalidState(msg) => {
                UnifiedError::Business(BusinessError::StateInconsistency {
                    message: msg,
                    expected_state: "unknown".to_string(),
                    actual_state: "unknown".to_string(),
                    entity_id: None,
                    details,
                })
            },
            crate::errors::SigmaXError::InvalidOperation(msg) => {
                UnifiedError::Business(BusinessError::StateInconsistency {
                    message: msg,
                    expected_state: "valid_operation".to_string(),
                    actual_state: "invalid_operation".to_string(),
                    entity_id: None,
                    details,
                })
            },
            crate::errors::SigmaXError::Service(msg) => {
                UnifiedError::Technical(TechnicalError::Internal {
                    message: msg,
                    component: "service".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::ValidationError(msg) => {
                UnifiedError::Validation(ValidationError::Field {
                    field: "unknown".to_string(),
                    message: msg,
                    value: None,
                    constraint: "validation".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::NotImplemented(msg) => {
                UnifiedError::Technical(TechnicalError::Internal {
                    message: format!("Not implemented: {}", msg),
                    component: "unknown".to_string(),
                    details,
                })
            },
            crate::errors::SigmaXError::RateLimitExceeded(msg) => {
                UnifiedError::External(ExternalError::RateLimit {
                    message: msg,
                    service: "unknown".to_string(),
                    limit: None,
                    reset_time: None,
                    details,
                })
            },
            crate::errors::SigmaXError::CircuitBreakerOpen(msg) => {
                UnifiedError::External(ExternalError::ServiceUnavailable {
                    message: msg,
                    service: "circuit_breaker".to_string(),
                    retry_after: None,
                    details,
                })
            },
        }
    }
}

/// 从标准库错误转换
impl From<std::io::Error> for UnifiedError {
    fn from(err: std::io::Error) -> Self {
        let mut details = HashMap::new();
        details.insert("kind".to_string(), format!("{:?}", err.kind()));
        
        UnifiedError::Technical(TechnicalError::Internal {
            message: err.to_string(),
            component: "io".to_string(),
            details,
        })
    }
}

impl From<serde_json::Error> for UnifiedError {
    fn from(err: serde_json::Error) -> Self {
        let mut details = HashMap::new();
        details.insert("line".to_string(), err.line().to_string());
        details.insert("column".to_string(), err.column().to_string());
        
        UnifiedError::Technical(TechnicalError::Serialization {
            message: err.to_string(),
            data_type: "json".to_string(),
            format: "json".to_string(),
            details,
        })
    }
}

impl From<sqlx::Error> for UnifiedError {
    fn from(err: sqlx::Error) -> Self {
        let mut details = HashMap::new();
        
        match &err {
            sqlx::Error::Database(db_err) => {
                if let Some(code) = db_err.code() {
                    details.insert("error_code".to_string(), code.to_string());
                }
                if let Some(constraint) = db_err.constraint() {
                    details.insert("constraint".to_string(), constraint.to_string());
                }
            },
            sqlx::Error::PoolTimedOut => {
                return UnifiedError::Technical(TechnicalError::Timeout {
                    message: "Database connection pool timed out".to_string(),
                    operation: "database_connection".to_string(),
                    timeout_ms: 30000, // 假设30秒超时
                    details,
                });
            },
            _ => {},
        }
        
        UnifiedError::Technical(TechnicalError::Database {
            message: err.to_string(),
            operation: "unknown".to_string(),
            table: None,
            details,
        })
    }
}

#[cfg(feature = "redis")]
impl From<redis::RedisError> for UnifiedError {
    fn from(err: redis::RedisError) -> Self {
        let mut details = HashMap::new();
        details.insert("kind".to_string(), format!("{:?}", err.kind()));

        UnifiedError::Technical(TechnicalError::Cache {
            message: err.to_string(),
            cache_type: "redis".to_string(),
            operation: "unknown".to_string(),
            key: None,
            details,
        })
    }
}

#[cfg(feature = "reqwest")]
impl From<reqwest::Error> for UnifiedError {
    fn from(err: reqwest::Error) -> Self {
        let mut details = HashMap::new();

        if let Some(url) = err.url() {
            details.insert("url".to_string(), url.to_string());
        }

        if err.is_timeout() {
            return UnifiedError::Technical(TechnicalError::Timeout {
                message: "HTTP request timed out".to_string(),
                operation: "http_request".to_string(),
                timeout_ms: 30000, // 假设30秒超时
                details,
            });
        }

        if err.is_connect() {
            return UnifiedError::Technical(TechnicalError::Network {
                message: err.to_string(),
                endpoint: err.url().map(|u| u.to_string()),
                status_code: err.status().map(|s| s.as_u16()),
                details,
            });
        }

        UnifiedError::External(ExternalError::Api {
            message: err.to_string(),
            service: "http_client".to_string(),
            endpoint: err.url().map(|u| u.to_string()).unwrap_or_default(),
            status_code: err.status().map(|s| s.as_u16()),
            details,
        })
    }
}

#[cfg(feature = "config")]
impl From<config::ConfigError> for UnifiedError {
    fn from(err: config::ConfigError) -> Self {
        let details = HashMap::new();

        UnifiedError::Technical(TechnicalError::Configuration {
            message: err.to_string(),
            config_key: None,
            config_file: None,
            details,
        })
    }
}

impl From<uuid::Error> for UnifiedError {
    fn from(err: uuid::Error) -> Self {
        let details = HashMap::new();

        UnifiedError::Validation(ValidationError::Format {
            message: err.to_string(),
            expected_format: "UUID".to_string(),
            actual_value: "invalid".to_string(),
            details,
        })
    }
}

impl From<chrono::ParseError> for UnifiedError {
    fn from(err: chrono::ParseError) -> Self {
        let details = HashMap::new();

        UnifiedError::Validation(ValidationError::Format {
            message: err.to_string(),
            expected_format: "DateTime".to_string(),
            actual_value: "invalid".to_string(),
            details,
        })
    }
}

/// 便利宏，用于快速创建业务错误
#[macro_export]
macro_rules! business_error {
    (order, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Business(
            $crate::errors::unified::BusinessError::Order {
                message: $msg.to_string(),
                order_id: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
    (strategy, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Business(
            $crate::errors::unified::BusinessError::Strategy {
                message: $msg.to_string(),
                strategy_id: None,
                strategy_type: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
    (risk, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Business(
            $crate::errors::unified::BusinessError::Risk {
                message: $msg.to_string(),
                risk_type: "unknown".to_string(),
                threshold: None,
                current_value: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
}

/// 便利宏，用于快速创建技术错误
#[macro_export]
macro_rules! technical_error {
    (database, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Technical(
            $crate::errors::unified::TechnicalError::Database {
                message: $msg.to_string(),
                operation: "unknown".to_string(),
                table: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
    (network, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Technical(
            $crate::errors::unified::TechnicalError::Network {
                message: $msg.to_string(),
                endpoint: None,
                status_code: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
    (config, $msg:expr) => {
        $crate::errors::unified::UnifiedError::Technical(
            $crate::errors::unified::TechnicalError::Configuration {
                message: $msg.to_string(),
                config_key: None,
                config_file: None,
                details: std::collections::HashMap::new(),
            }
        )
    };
}
