//! 统一错误处理系统
//!
//! 提供分层的、类型安全的、可扩展的错误处理机制
//!
//! ## 设计原则
//! - **分层设计**: 业务错误、技术错误、外部错误分离
//! - **类型安全**: 强类型错误，编译时检查
//! - **可扩展**: 易于添加新的错误类型
//! - **上下文丰富**: 包含详细的错误上下文信息
//! - **用户友好**: 提供用户可读的错误消息

use thiserror::Error;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// 统一错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum UnifiedError {
    /// 业务逻辑错误
    #[error("Business error: {0}")]
    Business(#[from] BusinessError),
    
    /// 技术系统错误
    #[error("Technical error: {0}")]
    Technical(#[from] TechnicalError),
    
    /// 外部服务错误
    #[error("External error: {0}")]
    External(#[from] ExternalError),
    
    /// 验证错误
    #[error("Validation error: {0}")]
    Validation(#[from] ValidationError),
}

/// 业务逻辑错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum BusinessError {
    /// 订单相关错误
    #[error("Order error: {message}")]
    Order { 
        message: String,
        order_id: Option<Uuid>,
        details: HashMap<String, String>,
    },
    
    /// 策略相关错误
    #[error("Strategy error: {message}")]
    Strategy { 
        message: String,
        strategy_id: Option<Uuid>,
        strategy_type: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 风险管理错误
    #[error("Risk management error: {message}")]
    Risk { 
        message: String,
        risk_type: String,
        threshold: Option<String>,
        current_value: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 投资组合错误
    #[error("Portfolio error: {message}")]
    Portfolio { 
        message: String,
        portfolio_id: Option<Uuid>,
        asset: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 交易所错误
    #[error("Exchange error: {message}")]
    Exchange { 
        message: String,
        exchange_id: Option<String>,
        symbol: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 资源不足错误
    #[error("Insufficient resources: {message}")]
    InsufficientResources { 
        message: String,
        resource_type: String,
        required: Option<String>,
        available: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 权限错误
    #[error("Permission denied: {message}")]
    PermissionDenied { 
        message: String,
        required_permission: String,
        user_id: Option<Uuid>,
        details: HashMap<String, String>,
    },
    
    /// 状态不一致错误
    #[error("State inconsistency: {message}")]
    StateInconsistency { 
        message: String,
        expected_state: String,
        actual_state: String,
        entity_id: Option<Uuid>,
        details: HashMap<String, String>,
    },
}

/// 技术系统错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum TechnicalError {
    /// 数据库错误
    #[error("Database error: {message}")]
    Database { 
        message: String,
        operation: String,
        table: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 网络错误
    #[error("Network error: {message}")]
    Network { 
        message: String,
        endpoint: Option<String>,
        status_code: Option<u16>,
        details: HashMap<String, String>,
    },
    
    /// 序列化错误
    #[error("Serialization error: {message}")]
    Serialization { 
        message: String,
        data_type: String,
        format: String,
        details: HashMap<String, String>,
    },
    
    /// 配置错误
    #[error("Configuration error: {message}")]
    Configuration { 
        message: String,
        config_key: Option<String>,
        config_file: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 缓存错误
    #[error("Cache error: {message}")]
    Cache { 
        message: String,
        cache_type: String,
        operation: String,
        key: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 并发错误
    #[error("Concurrency error: {message}")]
    Concurrency { 
        message: String,
        resource: String,
        operation: String,
        details: HashMap<String, String>,
    },
    
    /// 超时错误
    #[error("Timeout error: {message}")]
    Timeout { 
        message: String,
        operation: String,
        timeout_ms: u64,
        details: HashMap<String, String>,
    },
    
    /// 内部系统错误
    #[error("Internal error: {message}")]
    Internal { 
        message: String,
        component: String,
        details: HashMap<String, String>,
    },
}

/// 外部服务错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum ExternalError {
    /// 第三方API错误
    #[error("API error: {message}")]
    Api { 
        message: String,
        service: String,
        endpoint: String,
        status_code: Option<u16>,
        details: HashMap<String, String>,
    },
    
    /// 市场数据错误
    #[error("Market data error: {message}")]
    MarketData { 
        message: String,
        provider: String,
        symbol: Option<String>,
        data_type: String,
        details: HashMap<String, String>,
    },
    
    /// 交易所API错误
    #[error("Exchange API error: {message}")]
    ExchangeApi { 
        message: String,
        exchange: String,
        endpoint: String,
        error_code: Option<String>,
        details: HashMap<String, String>,
    },
    
    /// 服务不可用错误
    #[error("Service unavailable: {message}")]
    ServiceUnavailable { 
        message: String,
        service: String,
        retry_after: Option<u64>,
        details: HashMap<String, String>,
    },
    
    /// 限流错误
    #[error("Rate limit exceeded: {message}")]
    RateLimit { 
        message: String,
        service: String,
        limit: Option<u64>,
        reset_time: Option<DateTime<Utc>>,
        details: HashMap<String, String>,
    },
}

/// 验证错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum ValidationError {
    /// 字段验证错误
    #[error("Field validation error: {field} - {message}")]
    Field { 
        field: String,
        message: String,
        value: Option<String>,
        constraint: String,
        details: HashMap<String, String>,
    },
    
    /// 格式错误
    #[error("Format error: {message}")]
    Format { 
        message: String,
        expected_format: String,
        actual_value: String,
        details: HashMap<String, String>,
    },
    
    /// 范围错误
    #[error("Range error: {message}")]
    Range { 
        message: String,
        field: String,
        min: Option<String>,
        max: Option<String>,
        actual: String,
        details: HashMap<String, String>,
    },
    
    /// 必填字段错误
    #[error("Required field missing: {field}")]
    Required { 
        field: String,
        context: String,
        details: HashMap<String, String>,
    },
    
    /// 唯一性约束错误
    #[error("Uniqueness constraint violation: {field}")]
    Uniqueness { 
        field: String,
        value: String,
        context: String,
        details: HashMap<String, String>,
    },
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 信息级别 - 不影响系统运行
    Info,
    /// 警告级别 - 可能影响性能但不影响功能
    Warning,
    /// 错误级别 - 影响功能但系统可继续运行
    Error,
    /// 严重级别 - 可能导致系统不稳定
    Critical,
    /// 致命级别 - 系统无法继续运行
    Fatal,
}

/// 错误类别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 业务逻辑
    Business,
    /// 系统技术
    Technical,
    /// 外部服务
    External,
    /// 数据验证
    Validation,
}

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 错误发生的模块
    pub module: String,
    /// 错误发生的函数
    pub function: String,
    /// 错误发生的文件
    pub file: String,
    /// 错误发生的行号
    pub line: u32,
    /// 用户ID（如果有）
    pub user_id: Option<Uuid>,
    /// 会话ID（如果有）
    pub session_id: Option<String>,
    /// 请求ID（如果有）
    pub request_id: Option<String>,
    /// 额外的上下文信息
    pub metadata: HashMap<String, String>,
}

/// 统一结果类型
pub type UnifiedResult<T> = Result<T, UnifiedError>;

impl UnifiedError {
    /// 获取错误严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            UnifiedError::Business(err) => err.severity(),
            UnifiedError::Technical(err) => err.severity(),
            UnifiedError::External(err) => err.severity(),
            UnifiedError::Validation(_) => ErrorSeverity::Warning,
        }
    }

    /// 获取错误类别
    pub fn category(&self) -> ErrorCategory {
        match self {
            UnifiedError::Business(_) => ErrorCategory::Business,
            UnifiedError::Technical(_) => ErrorCategory::Technical,
            UnifiedError::External(_) => ErrorCategory::External,
            UnifiedError::Validation(_) => ErrorCategory::Validation,
        }
    }

    /// 判断错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            UnifiedError::Business(err) => err.is_retryable(),
            UnifiedError::Technical(err) => err.is_retryable(),
            UnifiedError::External(err) => err.is_retryable(),
            UnifiedError::Validation(_) => false,
        }
    }

    /// 获取错误代码
    pub fn error_code(&self) -> String {
        match self {
            UnifiedError::Business(err) => format!("BUS_{}", err.error_code()),
            UnifiedError::Technical(err) => format!("TEC_{}", err.error_code()),
            UnifiedError::External(err) => format!("EXT_{}", err.error_code()),
            UnifiedError::Validation(err) => format!("VAL_{}", err.error_code()),
        }
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            UnifiedError::Business(err) => err.user_message(),
            UnifiedError::Technical(err) => err.user_message(),
            UnifiedError::External(err) => err.user_message(),
            UnifiedError::Validation(err) => err.user_message(),
        }
    }

    /// 添加错误上下文
    pub fn with_context(self, context: ErrorContext) -> EnhancedError {
        EnhancedError {
            error: self,
            context,
            timestamp: Utc::now(),
            trace_id: Uuid::new_v4(),
        }
    }
}

impl BusinessError {
    /// 获取业务错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            BusinessError::Order { .. } => ErrorSeverity::Error,
            BusinessError::Strategy { .. } => ErrorSeverity::Error,
            BusinessError::Risk { .. } => ErrorSeverity::Critical,
            BusinessError::Portfolio { .. } => ErrorSeverity::Error,
            BusinessError::Exchange { .. } => ErrorSeverity::Error,
            BusinessError::InsufficientResources { .. } => ErrorSeverity::Warning,
            BusinessError::PermissionDenied { .. } => ErrorSeverity::Error,
            BusinessError::StateInconsistency { .. } => ErrorSeverity::Critical,
        }
    }

    /// 判断业务错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            BusinessError::Order { .. } => false,
            BusinessError::Strategy { .. } => true,
            BusinessError::Risk { .. } => false,
            BusinessError::Portfolio { .. } => true,
            BusinessError::Exchange { .. } => true,
            BusinessError::InsufficientResources { .. } => false,
            BusinessError::PermissionDenied { .. } => false,
            BusinessError::StateInconsistency { .. } => true,
        }
    }

    /// 获取业务错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            BusinessError::Order { .. } => 1001,
            BusinessError::Strategy { .. } => 1002,
            BusinessError::Risk { .. } => 1003,
            BusinessError::Portfolio { .. } => 1004,
            BusinessError::Exchange { .. } => 1005,
            BusinessError::InsufficientResources { .. } => 1006,
            BusinessError::PermissionDenied { .. } => 1007,
            BusinessError::StateInconsistency { .. } => 1008,
        }
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            BusinessError::Order { .. } => "订单处理失败，请检查订单参数".to_string(),
            BusinessError::Strategy { .. } => "策略执行出现问题，请检查策略配置".to_string(),
            BusinessError::Risk { .. } => "风险控制触发，操作被阻止".to_string(),
            BusinessError::Portfolio { .. } => "投资组合操作失败，请稍后重试".to_string(),
            BusinessError::Exchange { .. } => "交易所连接出现问题，请稍后重试".to_string(),
            BusinessError::InsufficientResources { .. } => "资源不足，请检查余额或权限".to_string(),
            BusinessError::PermissionDenied { .. } => "访问权限不足，请检查认证信息".to_string(),
            BusinessError::StateInconsistency { .. } => "系统状态异常，请联系技术支持".to_string(),
        }
    }
}

impl TechnicalError {
    /// 获取技术错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            TechnicalError::Database { .. } => ErrorSeverity::Critical,
            TechnicalError::Network { .. } => ErrorSeverity::Error,
            TechnicalError::Serialization { .. } => ErrorSeverity::Error,
            TechnicalError::Configuration { .. } => ErrorSeverity::Critical,
            TechnicalError::Cache { .. } => ErrorSeverity::Warning,
            TechnicalError::Concurrency { .. } => ErrorSeverity::Error,
            TechnicalError::Timeout { .. } => ErrorSeverity::Warning,
            TechnicalError::Internal { .. } => ErrorSeverity::Fatal,
        }
    }

    /// 判断技术错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            TechnicalError::Database { .. } => true,
            TechnicalError::Network { .. } => true,
            TechnicalError::Serialization { .. } => false,
            TechnicalError::Configuration { .. } => false,
            TechnicalError::Cache { .. } => true,
            TechnicalError::Concurrency { .. } => true,
            TechnicalError::Timeout { .. } => true,
            TechnicalError::Internal { .. } => false,
        }
    }

    /// 获取技术错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            TechnicalError::Database { .. } => 2001,
            TechnicalError::Network { .. } => 2002,
            TechnicalError::Serialization { .. } => 2003,
            TechnicalError::Configuration { .. } => 2004,
            TechnicalError::Cache { .. } => 2005,
            TechnicalError::Concurrency { .. } => 2006,
            TechnicalError::Timeout { .. } => 2007,
            TechnicalError::Internal { .. } => 2008,
        }
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            TechnicalError::Database { .. } => "数据库连接出现问题，请稍后重试".to_string(),
            TechnicalError::Network { .. } => "网络连接不稳定，请检查网络设置".to_string(),
            TechnicalError::Serialization { .. } => "数据格式错误，请检查输入数据".to_string(),
            TechnicalError::Configuration { .. } => "系统配置错误，请联系技术支持".to_string(),
            TechnicalError::Cache { .. } => "缓存服务暂时不可用，请稍后重试".to_string(),
            TechnicalError::Concurrency { .. } => "系统繁忙，请稍后重试".to_string(),
            TechnicalError::Timeout { .. } => "操作超时，请稍后重试".to_string(),
            TechnicalError::Internal { .. } => "系统内部错误，请联系技术支持".to_string(),
        }
    }
}

impl ExternalError {
    /// 获取外部错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            ExternalError::Api { .. } => ErrorSeverity::Error,
            ExternalError::MarketData { .. } => ErrorSeverity::Warning,
            ExternalError::ExchangeApi { .. } => ErrorSeverity::Error,
            ExternalError::ServiceUnavailable { .. } => ErrorSeverity::Error,
            ExternalError::RateLimit { .. } => ErrorSeverity::Warning,
        }
    }

    /// 判断外部错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            ExternalError::Api { .. } => true,
            ExternalError::MarketData { .. } => true,
            ExternalError::ExchangeApi { .. } => true,
            ExternalError::ServiceUnavailable { .. } => true,
            ExternalError::RateLimit { .. } => true,
        }
    }

    /// 获取外部错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            ExternalError::Api { .. } => 3001,
            ExternalError::MarketData { .. } => 3002,
            ExternalError::ExchangeApi { .. } => 3003,
            ExternalError::ServiceUnavailable { .. } => 3004,
            ExternalError::RateLimit { .. } => 3005,
        }
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            ExternalError::Api { .. } => "外部服务暂时不可用，请稍后重试".to_string(),
            ExternalError::MarketData { .. } => "市场数据获取失败，请稍后重试".to_string(),
            ExternalError::ExchangeApi { .. } => "交易所API调用失败，请稍后重试".to_string(),
            ExternalError::ServiceUnavailable { .. } => "服务暂时不可用，请稍后重试".to_string(),
            ExternalError::RateLimit { .. } => "请求过于频繁，请稍后重试".to_string(),
        }
    }
}

impl ValidationError {
    /// 获取验证错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            ValidationError::Field { .. } => 4001,
            ValidationError::Format { .. } => 4002,
            ValidationError::Range { .. } => 4003,
            ValidationError::Required { .. } => 4004,
            ValidationError::Uniqueness { .. } => 4005,
        }
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            ValidationError::Field { field, message, .. } => {
                format!("字段 '{}' 验证失败: {}", field, message)
            },
            ValidationError::Format { expected_format, .. } => {
                format!("数据格式错误，期望格式: {}", expected_format)
            },
            ValidationError::Range { field, min, max, .. } => {
                match (min, max) {
                    (Some(min), Some(max)) => format!("字段 '{}' 值必须在 {} 到 {} 之间", field, min, max),
                    (Some(min), None) => format!("字段 '{}' 值必须大于等于 {}", field, min),
                    (None, Some(max)) => format!("字段 '{}' 值必须小于等于 {}", field, max),
                    (None, None) => format!("字段 '{}' 值超出有效范围", field),
                }
            },
            ValidationError::Required { field, .. } => {
                format!("必填字段 '{}' 不能为空", field)
            },
            ValidationError::Uniqueness { field, .. } => {
                format!("字段 '{}' 的值已存在，必须唯一", field)
            },
        }
    }
}

/// 增强的错误类型，包含完整的上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedError {
    /// 原始错误
    pub error: UnifiedError,
    /// 错误上下文
    pub context: ErrorContext,
    /// 错误发生时间
    pub timestamp: DateTime<Utc>,
    /// 错误追踪ID
    pub trace_id: Uuid,
}

impl std::fmt::Display for EnhancedError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "[{}] {} in {}::{} ({}:{})",
            self.error.error_code(),
            self.error,
            self.context.module,
            self.context.function,
            self.context.file,
            self.context.line
        )
    }
}

impl std::error::Error for EnhancedError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.error)
    }
}

impl EnhancedError {
    /// 创建新的增强错误
    pub fn new(error: UnifiedError, context: ErrorContext) -> Self {
        Self {
            error,
            context,
            timestamp: Utc::now(),
            trace_id: Uuid::new_v4(),
        }
    }

    /// 获取错误严重程度
    pub fn severity(&self) -> ErrorSeverity {
        self.error.severity()
    }

    /// 获取错误类别
    pub fn category(&self) -> ErrorCategory {
        self.error.category()
    }

    /// 判断错误是否可重试
    pub fn is_retryable(&self) -> bool {
        self.error.is_retryable()
    }

    /// 获取错误代码
    pub fn error_code(&self) -> String {
        self.error.error_code()
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        self.error.user_message()
    }

    /// 添加额外的元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.context.metadata.insert(key, value);
        self
    }

    /// 转换为JSON格式
    pub fn to_json(&self) -> serde_json::Result<String> {
        serde_json::to_string_pretty(self)
    }
}
