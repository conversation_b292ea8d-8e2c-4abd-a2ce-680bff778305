use axum::{
    extract::{Path, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};
use crate::{
    error::{ApiError, ApiResult, ApiResponse},
    AppState,
};

/// 获取策略模板列表的查询参数
#[derive(Debug, Deserialize)]
pub struct TemplateListQuery {
    pub category: Option<String>,
    pub difficulty: Option<String>,
    pub include_schema: Option<bool>,
}

/// 策略模板响应
#[derive(Debug, Serialize)]
pub struct StrategyTemplateResponse {
    pub id: String,
    pub name: String,
    pub strategy_type: String,
    pub description: String,
    pub category: String,
    pub difficulty_level: String,
    pub risk_level: String,
    pub recommended_capital: Option<f64>,
    pub recommended_timeframes: Vec<String>,
    pub tags: Vec<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_config: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub parameter_schema: Option<serde_json::Value>,
}

/// 策略模板列表响应
#[derive(Debug, Serialize)]
pub struct TemplateListResponse {
    pub templates: Vec<StrategyTemplateResponse>,
    pub total_count: usize,
    pub categories: Vec<String>,
    pub difficulty_levels: Vec<String>,
}

/// 获取所有策略模板
/// GET /api/v1/strategy-templates
pub async fn get_strategy_templates(
    Query(query): Query<TemplateListQuery>,
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<TemplateListResponse>>> {
    info!("获取策略模板列表，查询参数: {:?}", query);

    // 尝试从数据库获取，如果失败则使用默认模板
    let mut templates = match get_templates_from_database(&state).await {
        Ok(db_templates) if !db_templates.is_empty() => db_templates,
        Ok(_) | Err(_) => {
            warn!("无法从数据库获取策略模板，使用默认模板");
            get_default_templates()
        }
    };

    // 根据类别过滤
    if let Some(ref category) = query.category {
        templates = templates.into_iter()
            .filter(|t| t.category == *category)
            .collect();
    }

    // 根据难度过滤
    if let Some(ref difficulty) = query.difficulty {
        templates = templates.into_iter()
            .filter(|t| t.difficulty_level == *difficulty)
            .collect();
    }

    let include_schema = query.include_schema.unwrap_or(false);

    // 如果不包含schema，清空相关字段
    if !include_schema {
        for template in &mut templates {
            template.default_config = None;
            template.parameter_schema = None;
        }
    }

    let categories = vec![
        "趋势跟踪".to_string(),
        "均值回归".to_string(),
        "套利策略".to_string(),
        "风险管理".to_string(),
    ];

    let difficulty_levels = vec![
        "新手友好".to_string(),
        "中级".to_string(),
        "高级".to_string(),
        "专家".to_string(),
    ];

    let response = TemplateListResponse {
        total_count: templates.len(),
        templates,
        categories,
        difficulty_levels,
    };

    info!("返回 {} 个策略模板", response.total_count);
    Ok(Json(ApiResponse::success(response, "获取策略模板列表成功")))
}

/// 从数据库获取策略模板
async fn get_templates_from_database(state: &AppState) -> ApiResult<Vec<StrategyTemplateResponse>> {
    // TODO: 实现数据库查询逻辑
    // 这里应该调用策略模板服务来获取数据
    // let templates = state.strategy_template_service.get_all_templates().await?;

    // 暂时返回空，表示数据库中没有数据
    Ok(Vec::new())
}

/// 获取硬编码的策略模板列表（用于验证和推荐配置）
fn get_hardcoded_templates() -> Vec<StrategyTemplateResponse> {
    get_default_templates()
}

/// 获取默认的策略模板列表（用作后备方案）
fn get_default_templates() -> Vec<StrategyTemplateResponse> {
    vec![
        StrategyTemplateResponse {
            id: "grid_trading_basic".to_string(),
            name: "基础网格交易".to_string(),
            strategy_type: "grid_trading".to_string(),
            description: "适合震荡市场的网格交易策略，通过在价格区间内设置买卖网格来获利".to_string(),
            category: "趋势跟踪".to_string(),
            difficulty_level: "新手友好".to_string(),
            risk_level: "中等风险".to_string(),
            recommended_capital: Some(10000.0),
            recommended_timeframes: vec!["1h".to_string(), "4h".to_string(), "1d".to_string()],
            tags: vec!["网格".to_string(), "震荡".to_string(), "稳健".to_string()],
            default_config: Some(serde_json::json!({
                "grid_count": 10,
                "price_range_percent": 20.0,
                "investment_per_grid": 1000.0,
                "stop_loss_percent": 15.0,
                "take_profit_percent": 25.0
            })),
            parameter_schema: Some(serde_json::json!({
                "grid_count": {
                    "type": "integer",
                    "min": 5,
                    "max": 50,
                    "description": "网格数量"
                },
                "price_range_percent": {
                    "type": "number",
                    "min": 5.0,
                    "max": 50.0,
                    "description": "价格区间百分比"
                }
            })),
        },
        StrategyTemplateResponse {
            id: "dynamic_grid_advanced".to_string(),
            name: "高级动态网格".to_string(),
            strategy_type: "grid_trading".to_string(),
            description: "具备动态调整能力的高级网格策略，支持波动率因子和自适应间距".to_string(),
            category: "趋势跟踪".to_string(),
            difficulty_level: "高级".to_string(),
            risk_level: "中高风险".to_string(),
            recommended_capital: Some(15000.0),
            recommended_timeframes: vec!["1h".to_string(), "4h".to_string(), "1d".to_string()],
            tags: vec!["网格".to_string(), "动态调整".to_string(), "高级".to_string()],
            default_config: Some(serde_json::json!({
                "grid_levels": 20,
                "price_range": 0.12,
                "reference_price": 400.0,
                "order_size_percentage": 0.08,
                "grid_spacing": 0.003,
                "rebalance_threshold": 0.015,
                "dynamic_adjustment": true,
                "volatility_factor": 1.5,
                "adaptive_spacing": true,
                "risk_scaling": true
            })),
            parameter_schema: Some(serde_json::json!({
                "grid_levels": {
                    "type": "integer",
                    "min": 10,
                    "max": 50,
                    "description": "网格层数"
                },
                "price_range": {
                    "type": "number",
                    "min": 0.05,
                    "max": 0.3,
                    "description": "价格范围比例"
                },
                "volatility_factor": {
                    "type": "number",
                    "min": 1.0,
                    "max": 3.0,
                    "description": "波动率因子"
                }
            })),
        },
        StrategyTemplateResponse {
            id: "adaptive_volatility_grid".to_string(),
            name: "自适应波动率网格".to_string(),
            strategy_type: "grid_trading".to_string(),
            description: "基于8级波动率阈值的智能自适应网格策略，能够根据市场波动率动态调整网格参数".to_string(),
            category: "趋势跟踪".to_string(),
            difficulty_level: "专家".to_string(),
            risk_level: "高风险".to_string(),
            recommended_capital: Some(25000.0),
            recommended_timeframes: vec!["1h".to_string(), "4h".to_string(), "1d".to_string()],
            tags: vec!["网格".to_string(), "自适应".to_string(), "波动率".to_string(), "专业".to_string()],
            default_config: Some(serde_json::json!({
                "grid_levels": 15,
                "reference_price": 400.0,
                "order_amount": 0.05,
                "grid_spacing_percent": 0.005,
                "volatility_window": 24,
                "grid_params": {
                    "initial": 0.8,
                    "min": 0.8,
                    "max": 3.0,
                    "adjust_threshold": 0.2,
                    "cooldown": 0,
                    "volatility_threshold": {
                        "ranges": [
                            {
                                "grid": 1.0,
                                "range": [0.0, 0.002]
                            },
                            {
                                "grid": 1.5,
                                "range": [0.002, 0.004]
                            },
                            {
                                "grid": 2.0,
                                "range": [0.004, 0.006]
                            },
                            {
                                "grid": 2.5,
                                "range": [0.006, 0.008]
                            },
                            {
                                "grid": 3.0,
                                "range": [0.008, 0.010]
                            },
                            {
                                "grid": 3.5,
                                "range": [0.010, 0.012]
                            },
                            {
                                "grid": 4.0,
                                "range": [0.012, 999.0]
                            }
                        ]
                    }
                },
                "constraints": {
                    "max_grid_spacing": 0.02,
                    "min_grid_spacing": 0.001,
                    "max_order_amount": 1.0,
                    "max_volatility_window": 168,
                    "min_volatility_window": 1
                }
            })),
            parameter_schema: Some(serde_json::json!({
                "grid_levels": {
                    "type": "integer",
                    "min": 10,
                    "max": 30,
                    "description": "网格层数"
                },
                "order_amount": {
                    "type": "number",
                    "min": 0.01,
                    "max": 0.2,
                    "description": "订单金额比例"
                },
                "volatility_window": {
                    "type": "integer",
                    "min": 1,
                    "max": 168,
                    "description": "波动率计算窗口(小时)"
                }
            })),
        },
        StrategyTemplateResponse {
            id: "dca_conservative".to_string(),
            name: "保守定投策略".to_string(),
            strategy_type: "dca".to_string(),
            description: "定期定额投资策略，适合长期持有和风险厌恶型投资者".to_string(),
            category: "均值回归".to_string(),
            difficulty_level: "新手友好".to_string(),
            risk_level: "低风险".to_string(),
            recommended_capital: Some(5000.0),
            recommended_timeframes: vec!["1d".to_string(), "1w".to_string()],
            tags: vec!["定投".to_string(), "长期".to_string(), "保守".to_string()],
            default_config: Some(serde_json::json!({
                "investment_amount": 500.0,
                "investment_interval": "daily",
                "max_investment_count": 20,
                "stop_loss_percent": 30.0
            })),
            parameter_schema: Some(serde_json::json!({
                "investment_amount": {
                    "type": "number",
                    "min": 100.0,
                    "max": 10000.0,
                    "description": "每次投资金额"
                }
            })),
        },
        StrategyTemplateResponse {
            id: "momentum_advanced".to_string(),
            name: "高级动量策略".to_string(),
            strategy_type: "momentum".to_string(),
            description: "基于技术指标的动量交易策略，适合趋势明显的市场环境".to_string(),
            category: "趋势跟踪".to_string(),
            difficulty_level: "高级".to_string(),
            risk_level: "高风险".to_string(),
            recommended_capital: Some(20000.0),
            recommended_timeframes: vec!["15m".to_string(), "1h".to_string(), "4h".to_string()],
            tags: vec!["动量".to_string(), "趋势".to_string(), "技术分析".to_string()],
            default_config: Some(serde_json::json!({
                "rsi_period": 14,
                "rsi_overbought": 70,
                "rsi_oversold": 30,
                "position_size_percent": 10.0,
                "stop_loss_percent": 8.0,
                "take_profit_percent": 15.0
            })),
            parameter_schema: Some(serde_json::json!({
                "rsi_period": {
                    "type": "integer",
                    "min": 5,
                    "max": 50,
                    "description": "RSI周期"
                }
            })),
        },
        StrategyTemplateResponse {
            id: "mean_reversion_basic".to_string(),
            name: "基础均值回归".to_string(),
            strategy_type: "mean_reversion".to_string(),
            description: "基于价格回归均值的交易策略，适合横盘整理的市场".to_string(),
            category: "均值回归".to_string(),
            difficulty_level: "中级".to_string(),
            risk_level: "中等风险".to_string(),
            recommended_capital: Some(15000.0),
            recommended_timeframes: vec!["1h".to_string(), "4h".to_string()],
            tags: vec!["均值回归".to_string(), "横盘".to_string(), "统计套利".to_string()],
            default_config: Some(serde_json::json!({
                "lookback_period": 20,
                "deviation_threshold": 2.0,
                "position_size_percent": 15.0,
                "stop_loss_percent": 10.0,
                "take_profit_percent": 12.0
            })),
            parameter_schema: Some(serde_json::json!({
                "lookback_period": {
                    "type": "integer",
                    "min": 10,
                    "max": 100,
                    "description": "回看周期"
                }
            })),
        },

        // 非对称波动率网格策略 - 保守型
        StrategyTemplateResponse {
            id: "asymmetric_grid_conservative".to_string(),
            name: "非对称网格-保守型".to_string(),
            strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
            description: "基于波动率的非对称网格策略，保守型配置[-1%,+3%]。下跌时密集吸筹，上涨时稀疏止盈，适合稳健投资者".to_string(),
            category: "网格交易".to_string(),
            difficulty_level: "新手友好".to_string(),
            risk_level: "低风险".to_string(),
            recommended_capital: Some(15000.0),
            recommended_timeframes: vec!["15m".to_string(), "1h".to_string(), "4h".to_string()],
            tags: vec!["非对称".to_string(), "波动率".to_string(), "保守".to_string(), "吸筹".to_string()],
            default_config: Some(serde_json::json!({
                "strategy_preset": "Conservative",
                "base_price": 0.0,
                "down_range_start": -0.01,
                "down_range_end": -0.02,
                "down_grid_count": 8,
                "down_base_quantity": 0.015,
                "up_range_start": 0.01,
                "up_range_end": 0.03,
                "up_grid_count": 4,
                "up_base_quantity": 0.01,
                "volatility_window_hours": 24,
                "volatility_multiplier": 1.0,
                "enable_dynamic_volatility": true,
                "max_total_position": 1.5,
                "down_zone_max_capital_ratio": 0.65,
                "max_loss_percentage": 5.0,
                "simulation_mode": true,
                "log_level": 2
            })),
            parameter_schema: Some(serde_json::json!({
                "strategy_preset": {
                    "type": "string",
                    "enum": ["Conservative", "Balanced", "Aggressive", "VeryAggressive", "Custom"],
                    "default": "Conservative",
                    "description": "预设策略类型"
                },
                "volatility_multiplier": {
                    "type": "number",
                    "min": 0.1,
                    "max": 5.0,
                    "default": 1.0,
                    "description": "波动率乘数"
                },
                "max_loss_percentage": {
                    "type": "number",
                    "min": 1.0,
                    "max": 50.0,
                    "default": 5.0,
                    "description": "最大亏损百分比"
                }
            })),
        },

        // 非对称波动率网格策略 - 平衡型
        StrategyTemplateResponse {
            id: "asymmetric_grid_balanced".to_string(),
            name: "非对称网格-平衡型".to_string(),
            strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
            description: "基于波动率的非对称网格策略，平衡型配置[-2%,+8%]。智能资金分配，风险收益平衡，适合大多数投资者".to_string(),
            category: "网格交易".to_string(),
            difficulty_level: "中级".to_string(),
            risk_level: "中等风险".to_string(),
            recommended_capital: Some(20000.0),
            recommended_timeframes: vec!["5m".to_string(), "15m".to_string(), "1h".to_string(), "4h".to_string()],
            tags: vec!["非对称".to_string(), "波动率".to_string(), "平衡".to_string(), "智能".to_string()],
            default_config: Some(serde_json::json!({
                "strategy_preset": "Balanced",
                "base_price": 0.0,
                "trading_pair": "BTC/USDT",
                "exchange_id": "simulator",
                "order_amount": 200.0,
                "down_range_start": -0.02,
                "down_range_end": -0.10,
                "down_grid_count": 8,
                "down_base_quantity": 0.15,
                "down_grid_distribution": {"Exponential": {"factor": 1.2}},
                "down_quantity_model": {"Linear": {"factor": 1.0}},
                "up_range_start": 0.02,
                "up_range_end": 0.15,
                "up_grid_count": 6,
                "up_base_quantity": 0.15,
                "up_grid_distribution": "Linear",
                "up_quantity_model": "Constant",
                "volatility_window_hours": 12,
                "volatility_multiplier": 1.0,
                "enable_dynamic_volatility": true,
                "max_position_amount": 2000.0,
                "max_daily_trades": 50,
                "stop_loss_config": {"Trailing": {"trail_percent": 0.10}}
            })),
            parameter_schema: Some(serde_json::json!({
                "strategy_preset": {
                    "type": "string",
                    "enum": ["Conservative", "Balanced", "Aggressive", "VeryAggressive", "Custom"],
                    "default": "Balanced",
                    "description": "预设策略类型"
                },
                "volatility_multiplier": {
                    "type": "number",
                    "min": 0.1,
                    "max": 5.0,
                    "default": 1.2,
                    "description": "波动率乘数"
                },
                "max_loss_percentage": {
                    "type": "number",
                    "min": 1.0,
                    "max": 50.0,
                    "default": 8.0,
                    "description": "最大亏损百分比"
                }
            })),
        },

        // 非对称波动率网格策略 - 激进型
        StrategyTemplateResponse {
            id: "asymmetric_grid_aggressive".to_string(),
            name: "非对称网格-激进型".to_string(),
            strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
            description: "基于波动率的非对称网格策略，激进型配置[-3%,+12%]。追求高收益，适合有经验的投资者".to_string(),
            category: "网格交易".to_string(),
            difficulty_level: "高级".to_string(),
            risk_level: "高风险".to_string(),
            recommended_capital: Some(30000.0),
            recommended_timeframes: vec!["1m".to_string(), "5m".to_string(), "15m".to_string(), "1h".to_string()],
            tags: vec!["非对称".to_string(), "波动率".to_string(), "激进".to_string(), "高收益".to_string()],
            default_config: Some(serde_json::json!({
                "strategy_preset": "Aggressive",
                "base_price": 0.0,
                "trading_pair": "BTC/USDT",
                "exchange_id": "simulator",
                "order_amount": 500.0,
                "down_range_start": -0.03,
                "down_range_end": -0.15,
                "down_grid_count": 12,
                "down_base_quantity": 0.2,
                "down_grid_distribution": {"Exponential": {"factor": 1.5}},
                "down_quantity_model": {"Exponential": {"base": 1.3}},
                "up_range_start": 0.03,
                "up_range_end": 0.25,
                "up_grid_count": 8,
                "up_base_quantity": 0.2,
                "up_grid_distribution": "Fibonacci",
                "up_quantity_model": {"Linear": {"factor": 0.5}},
                "volatility_window_hours": 6,
                "volatility_multiplier": 1.5,
                "enable_dynamic_volatility": true,
                "max_position_amount": 5000.0,
                "max_daily_trades": 100,
                "stop_loss_config": {"Hybrid": {"max_loss_percent": 0.25, "trail_percent": 0.08, "atr_multiplier": 2.0, "atr_period": 14}}
            })),
            parameter_schema: Some(serde_json::json!({
                "strategy_preset": {
                    "type": "string",
                    "enum": ["Conservative", "Balanced", "Aggressive", "VeryAggressive", "Custom"],
                    "default": "Aggressive",
                    "description": "预设策略类型"
                },
                "volatility_multiplier": {
                    "type": "number",
                    "min": 0.1,
                    "max": 5.0,
                    "default": 1.5,
                    "description": "波动率乘数"
                },
                "max_loss_percentage": {
                    "type": "number",
                    "min": 1.0,
                    "max": 50.0,
                    "default": 12.0,
                    "description": "最大亏损百分比"
                }
            })),
        },

        // 非对称波动率网格策略 - 超激进型
        StrategyTemplateResponse {
            id: "asymmetric_grid_very_aggressive".to_string(),
            name: "非对称网格-超激进型".to_string(),
            strategy_type: "asymmetric_volatility_grid_strategy".to_string(),
            description: "基于波动率的非对称网格策略，超激进型配置[-5%,+20%]。高风险高收益，适合专业交易者".to_string(),
            category: "网格交易".to_string(),
            difficulty_level: "专家".to_string(),
            risk_level: "极高风险".to_string(),
            recommended_capital: Some(50000.0),
            recommended_timeframes: vec!["1m".to_string(), "5m".to_string(), "15m".to_string()],
            tags: vec!["非对称".to_string(), "波动率".to_string(), "超激进".to_string(), "专业".to_string()],
            default_config: Some(serde_json::json!({
                "strategy_preset": "VeryAggressive",
                "base_price": 0.0,
                "down_range_start": -0.05,
                "down_range_end": -0.12,
                "down_grid_count": 20,
                "down_base_quantity": 0.03,
                "up_range_start": 0.05,
                "up_range_end": 0.20,
                "up_grid_count": 10,
                "up_base_quantity": 0.025,
                "volatility_window_hours": 48,
                "volatility_multiplier": 2.0,
                "enable_dynamic_volatility": true,
                "max_total_position": 5.0,
                "down_zone_max_capital_ratio": 0.8,
                "max_loss_percentage": 20.0,
                "simulation_mode": true,
                "log_level": 1
            })),
            parameter_schema: Some(serde_json::json!({
                "strategy_preset": {
                    "type": "string",
                    "enum": ["Conservative", "Balanced", "Aggressive", "VeryAggressive", "Custom"],
                    "default": "VeryAggressive",
                    "description": "预设策略类型"
                },
                "volatility_multiplier": {
                    "type": "number",
                    "min": 0.1,
                    "max": 5.0,
                    "default": 2.0,
                    "description": "波动率乘数"
                },
                "max_loss_percentage": {
                    "type": "number",
                    "min": 1.0,
                    "max": 50.0,
                    "default": 20.0,
                    "description": "最大亏损百分比"
                }
            })),
        },
    ]
}

/// 根据ID获取策略模板详情
/// GET /api/v1/strategy-templates/:template_id
pub async fn get_strategy_template_by_id(
    Path(template_id): Path<String>,
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<StrategyTemplateResponse>>> {
    info!("获取策略模板详情，ID: {}", template_id);

    // 尝试从数据库获取，如果失败则使用默认模板
    let templates = match get_templates_from_database(&state).await {
        Ok(db_templates) if !db_templates.is_empty() => db_templates,
        Ok(_) | Err(_) => get_default_templates(),
    };

    match templates.into_iter().find(|t| t.id == template_id) {
        Some(template) => {
            info!("返回策略模板详情: {}", template.name);
            Ok(Json(ApiResponse::success(template, "获取策略模板详情成功")))
        }
        None => {
            error!("策略模板不存在: {}", template_id);
            Err(ApiError::NotFound(format!("策略模板不存在: {}", template_id)))
        }
    }
}

/// 验证策略模板配置
/// POST /api/v1/strategy-templates/:template_id/validate
#[derive(Debug, Deserialize)]
pub struct ValidateTemplateConfigRequest {
    pub config: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct ValidateTemplateConfigResponse {
    pub valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub suggestions: Vec<String>,
}

pub async fn validate_strategy_template_config(
    Path(template_id): Path<String>,
    State(_state): State<AppState>,
    Json(request): Json<ValidateTemplateConfigRequest>,
) -> ApiResult<Json<ApiResponse<ValidateTemplateConfigResponse>>> {
    info!("验证策略模板配置，模板ID: {}", template_id);

    let templates = get_hardcoded_templates();

    let mut errors = Vec::new();
    let mut warnings = Vec::new();
    let mut suggestions = Vec::new();

    match templates.into_iter().find(|t| t.id == template_id) {
        Some(template) => {
            info!("策略模板配置验证通过");

            // 添加一些建议
            if let Some(recommended_capital) = template.recommended_capital {
                if let Some(initial_capital) = request.config.get("initial_capital") {
                    if let Some(capital_value) = initial_capital.as_f64() {
                        if capital_value < recommended_capital * 0.5 {
                            suggestions.push(format!("建议初始资金至少为 {:.0} USDT 以获得更好的策略效果", recommended_capital));
                        }
                    }
                }
            }

            // 检查时间框架建议
            if !template.recommended_timeframes.is_empty() {
                suggestions.push(format!("推荐时间框架: {}", template.recommended_timeframes.join(", ")));
            }

            // 基本配置验证
            if let Some(strategy_type) = request.config.get("strategy_type") {
                if strategy_type.as_str() != Some(&template.strategy_type) {
                    warnings.push("策略类型与模板不匹配".to_string());
                }
            }
        }
        None => {
            errors.push(format!("策略模板不存在: {}", template_id));
        }
    }

    let response = ValidateTemplateConfigResponse {
        valid: errors.is_empty(),
        errors,
        warnings,
        suggestions,
    };

    Ok(Json(ApiResponse::success(response, "配置验证完成")))
}

/// 获取策略模板的推荐配置
/// GET /api/v1/strategy-templates/:template_id/recommended-config
#[derive(Debug, Serialize)]
pub struct RecommendedConfigResponse {
    pub template_id: String,
    pub template_name: String,
    pub recommended_config: serde_json::Value,
    pub parameter_descriptions: std::collections::HashMap<String, String>,
    pub risk_warnings: Vec<String>,
}

pub async fn get_recommended_config(
    Path(template_id): Path<String>,
    State(_state): State<AppState>,
) -> ApiResult<Json<ApiResponse<RecommendedConfigResponse>>> {
    info!("获取策略模板推荐配置，模板ID: {}", template_id);

    let templates = get_hardcoded_templates();

    match templates.into_iter().find(|t| t.id == template_id) {
        Some(template) => {
            let mut parameter_descriptions = std::collections::HashMap::new();

            // 根据策略类型和模板ID添加参数描述
            match template.strategy_type.as_str() {
                "grid_trading" => {
                    match template.id.as_str() {
                        "grid_trading_basic" => {
                            parameter_descriptions.insert("grid_count".to_string(), "网格数量，决定交易频率".to_string());
                            parameter_descriptions.insert("price_range_percent".to_string(), "价格区间百分比，决定网格覆盖范围".to_string());
                            parameter_descriptions.insert("investment_per_grid".to_string(), "每个网格的投资金额".to_string());
                        },
                        "dynamic_grid_advanced" => {
                            parameter_descriptions.insert("grid_levels".to_string(), "网格层数，影响交易密度".to_string());
                            parameter_descriptions.insert("price_range".to_string(), "价格范围比例，决定网格覆盖区间".to_string());
                            parameter_descriptions.insert("volatility_factor".to_string(), "波动率因子，用于动态调整".to_string());
                            parameter_descriptions.insert("dynamic_adjustment".to_string(), "是否启用动态调整功能".to_string());
                            parameter_descriptions.insert("adaptive_spacing".to_string(), "是否启用自适应间距".to_string());
                        },
                        "adaptive_volatility_grid" => {
                            parameter_descriptions.insert("grid_levels".to_string(), "网格层数，建议10-30层".to_string());
                            parameter_descriptions.insert("order_amount".to_string(), "订单金额比例，建议0.01-0.2".to_string());
                            parameter_descriptions.insert("volatility_window".to_string(), "波动率计算窗口，单位小时".to_string());
                            parameter_descriptions.insert("grid_params".to_string(), "高级网格参数，包含8级波动率阈值".to_string());
                            parameter_descriptions.insert("constraints".to_string(), "约束条件，确保策略安全运行".to_string());
                        },
                        _ => {
                            parameter_descriptions.insert("grid_count".to_string(), "网格数量，决定交易频率".to_string());
                            parameter_descriptions.insert("price_range_percent".to_string(), "价格区间百分比，决定网格覆盖范围".to_string());
                        }
                    }
                }
                "dca" => {
                    parameter_descriptions.insert("investment_amount".to_string(), "每次定投金额".to_string());
                    parameter_descriptions.insert("investment_interval".to_string(), "定投间隔时间".to_string());
                }
                "momentum" => {
                    parameter_descriptions.insert("rsi_period".to_string(), "RSI指标周期".to_string());
                    parameter_descriptions.insert("position_size_percent".to_string(), "仓位大小百分比".to_string());
                }
                "mean_reversion" => {
                    parameter_descriptions.insert("lookback_period".to_string(), "回看周期，用于计算均值".to_string());
                    parameter_descriptions.insert("deviation_threshold".to_string(), "偏离阈值，触发交易信号".to_string());
                }
                _ => {}
            }

            let mut risk_warnings = Vec::new();
            match template.risk_level.as_str() {
                "高风险" => {
                    risk_warnings.push("⚠️ 高风险策略：可能导致较大亏损，请谨慎使用".to_string());
                }
                "中高风险" => {
                    risk_warnings.push("⚠️ 中高风险策略：波动较大，建议有经验的交易者使用".to_string());
                }
                _ => {}
            }

            if template.difficulty_level == "高级" || template.difficulty_level == "专家" {
                risk_warnings.push("⚠️ 该策略需要一定的交易经验，建议新手谨慎使用".to_string());
            }

            let response = RecommendedConfigResponse {
                template_id: template.id.clone(),
                template_name: template.name.clone(),
                recommended_config: template.default_config.unwrap_or_default(),
                parameter_descriptions,
                risk_warnings,
            };

            info!("返回策略模板推荐配置: {}", template.name);
            Ok(Json(ApiResponse::success(response, "获取推荐配置成功")))
        }
        None => {
            error!("策略模板不存在: {}", template_id);
            Err(ApiError::NotFound(format!("策略模板不存在: {}", template_id)))
        }
    }
}
