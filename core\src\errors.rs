//! SigmaX 错误处理系统
//!
//! 遵循核心设计原则的错误处理系统：
//! - 高内聚，低耦合：模块化错误设计
//! - 关注点分离：业务、基础设施、外部错误分离
//! - 面向接口设计：清晰的错误接口契约
//! - 可测试性设计：支持依赖注入和Mock测试
//! - 简洁与可演化性：保持简洁，支持扩展

pub mod domain;
pub mod infrastructure;
pub mod external;
pub mod handlers;

// 重新导出核心类型
pub use domain::{
    DomainError, ErrorCategory, ErrorSeverity,
    TradingError, StrategyError, RiskError, ValidationError,
};
pub use infrastructure::{
    DatabaseError, NetworkError, CacheError, ConfigurationError,
};
pub use external::{
    ExternalApiError, ExchangeApiError, MarketDataError, ThirdPartyServiceError,
};
pub use handlers::{
    ErrorHandler, ErrorHandlerChain, ErrorHandlerFactory, DefaultErrorHandlerFactory,
    ErrorHandlingResult, ErrorAction,
};

use thiserror::Error;
use std::collections::HashMap;

/// SigmaX 统一错误类型
///
/// 平衡方案：保持向后兼容的同时引入新的设计原则
#[derive(Error, Debug)]
pub enum SigmaXError {
    // === 新的领域错误（遵循设计原则）===
    #[error(transparent)]
    Trading(#[from] TradingError),

    #[error(transparent)]
    Strategy(#[from] StrategyError),

    #[error(transparent)]
    Risk(#[from] RiskError),

    #[error(transparent)]
    Validation(#[from] ValidationError),

    #[error(transparent)]
    Database(#[from] DatabaseError),

    #[error(transparent)]
    Network(#[from] NetworkError),

    #[error(transparent)]
    Cache(#[from] CacheError),

    #[error(transparent)]
    Configuration(#[from] ConfigurationError),

    #[error(transparent)]
    ExchangeApi(#[from] ExchangeApiError),

    #[error(transparent)]
    MarketData(#[from] MarketDataError),

    #[error(transparent)]
    ThirdPartyService(#[from] ThirdPartyServiceError),

    // === 向后兼容的旧错误变体 ===
    #[error("Exchange error: {0}")]
    Exchange(String),

    #[error("Order error: {0}")]
    Order(String),

    #[error("Data error: {0}")]
    Data(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Rate limit exceeded")]
    RateLimit,

    #[error("Internal error: {0}")]
    Internal(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[error("Invalid operation: {0}")]
    InvalidOperation(String),

    #[error("Invalid state: {0}")]
    InvalidState(String),

    #[error("Circuit breaker open: {0}")]
    CircuitBreakerOpen(String),

    #[error("Risk management error: {0}")]
    RiskManagement(String),
}

impl DomainError for SigmaXError {
    fn category(&self) -> ErrorCategory {
        match self {
            // 新的领域错误
            SigmaXError::Trading(e) => e.category(),
            SigmaXError::Strategy(e) => e.category(),
            SigmaXError::Risk(e) => e.category(),
            SigmaXError::Validation(e) => e.category(),
            SigmaXError::Database(e) => e.category(),
            SigmaXError::Network(e) => e.category(),
            SigmaXError::Cache(e) => e.category(),
            SigmaXError::Configuration(e) => e.category(),
            SigmaXError::ExchangeApi(e) => e.category(),
            SigmaXError::MarketData(e) => e.category(),
            SigmaXError::ThirdPartyService(e) => e.category(),

            // 向后兼容的旧错误
            SigmaXError::Exchange(_) => ErrorCategory::External,
            SigmaXError::Order(_) => ErrorCategory::Business,
            SigmaXError::Data(_) => ErrorCategory::Infrastructure,
            SigmaXError::Config(_) => ErrorCategory::Infrastructure,
            SigmaXError::Serialization(_) => ErrorCategory::Infrastructure,
            SigmaXError::InvalidParameter(_) => ErrorCategory::Validation,
            SigmaXError::NotFound(_) => ErrorCategory::Business,
            SigmaXError::Unauthorized => ErrorCategory::Business,
            SigmaXError::RateLimit => ErrorCategory::External,
            SigmaXError::Internal(_) => ErrorCategory::Infrastructure,
            SigmaXError::ValidationError(_) => ErrorCategory::Validation,
            SigmaXError::NotImplemented(_) => ErrorCategory::Infrastructure,
            SigmaXError::InvalidOperation(_) => ErrorCategory::Business,
            SigmaXError::InvalidState(_) => ErrorCategory::Business,
            SigmaXError::CircuitBreakerOpen(_) => ErrorCategory::Infrastructure,
            SigmaXError::RiskManagement(_) => ErrorCategory::Business,
        }
    }

    fn severity(&self) -> ErrorSeverity {
        match self {
            // 新的领域错误
            SigmaXError::Trading(e) => e.severity(),
            SigmaXError::Strategy(e) => e.severity(),
            SigmaXError::Risk(e) => e.severity(),
            SigmaXError::Validation(e) => e.severity(),
            SigmaXError::Database(e) => e.severity(),
            SigmaXError::Network(e) => e.severity(),
            SigmaXError::Cache(e) => e.severity(),
            SigmaXError::Configuration(e) => e.severity(),
            SigmaXError::ExchangeApi(e) => e.severity(),
            SigmaXError::MarketData(e) => e.severity(),
            SigmaXError::ThirdPartyService(e) => e.severity(),

            // 向后兼容的旧错误
            SigmaXError::Exchange(_) => ErrorSeverity::Warning,
            SigmaXError::Order(_) => ErrorSeverity::Error,
            SigmaXError::Data(_) => ErrorSeverity::Error,
            SigmaXError::Config(_) => ErrorSeverity::Critical,
            SigmaXError::Serialization(_) => ErrorSeverity::Error,
            SigmaXError::InvalidParameter(_) => ErrorSeverity::Error,
            SigmaXError::NotFound(_) => ErrorSeverity::Warning,
            SigmaXError::Unauthorized => ErrorSeverity::Error,
            SigmaXError::RateLimit => ErrorSeverity::Warning,
            SigmaXError::Internal(_) => ErrorSeverity::Critical,
            SigmaXError::ValidationError(_) => ErrorSeverity::Error,
            SigmaXError::NotImplemented(_) => ErrorSeverity::Error,
            SigmaXError::InvalidOperation(_) => ErrorSeverity::Error,
            SigmaXError::InvalidState(_) => ErrorSeverity::Error,
            SigmaXError::CircuitBreakerOpen(_) => ErrorSeverity::Warning,
            SigmaXError::RiskManagement(_) => ErrorSeverity::Error,
        }
    }

    fn error_code(&self) -> &'static str {
        match self {
            // 新的领域错误
            SigmaXError::Trading(e) => e.error_code(),
            SigmaXError::Strategy(e) => e.error_code(),
            SigmaXError::Risk(e) => e.error_code(),
            SigmaXError::Validation(e) => e.error_code(),
            SigmaXError::Database(e) => e.error_code(),
            SigmaXError::Network(e) => e.error_code(),
            SigmaXError::Cache(e) => e.error_code(),
            SigmaXError::Configuration(e) => e.error_code(),
            SigmaXError::ExchangeApi(e) => e.error_code(),
            SigmaXError::MarketData(e) => e.error_code(),
            SigmaXError::ThirdPartyService(e) => e.error_code(),

            // 向后兼容的旧错误
            SigmaXError::Exchange(_) => "LEGACY_EXCHANGE_ERROR",
            SigmaXError::Order(_) => "LEGACY_ORDER_ERROR",
            SigmaXError::Data(_) => "LEGACY_DATA_ERROR",
            SigmaXError::Config(_) => "LEGACY_CONFIG_ERROR",
            SigmaXError::Serialization(_) => "LEGACY_SERIALIZATION_ERROR",
            SigmaXError::InvalidParameter(_) => "LEGACY_INVALID_PARAMETER",
            SigmaXError::NotFound(_) => "LEGACY_NOT_FOUND",
            SigmaXError::Unauthorized => "LEGACY_UNAUTHORIZED",
            SigmaXError::RateLimit => "LEGACY_RATE_LIMIT",
            SigmaXError::Internal(_) => "LEGACY_INTERNAL_ERROR",
            SigmaXError::ValidationError(_) => "LEGACY_VALIDATION_ERROR",
            SigmaXError::NotImplemented(_) => "LEGACY_NOT_IMPLEMENTED",
            SigmaXError::InvalidOperation(_) => "LEGACY_INVALID_OPERATION",
            SigmaXError::InvalidState(_) => "LEGACY_INVALID_STATE",
            SigmaXError::CircuitBreakerOpen(_) => "LEGACY_CIRCUIT_BREAKER_OPEN",
            SigmaXError::RiskManagement(_) => "LEGACY_RISK_MANAGEMENT",
        }
    }
}

/// SigmaX 结果类型
pub type SigmaXResult<T> = Result<T, SigmaXError>;

/// 便利构造函数
impl SigmaXError {
    /// 创建交易错误
    pub fn trading_error(message: impl Into<String>, order_id: Option<uuid::Uuid>) -> Self {
        Self::Trading(TradingError::InvalidOrder {
            message: message.into(),
            order_id,
        })
    }
    
    /// 创建策略错误
    pub fn strategy_error(message: impl Into<String>, strategy_id: uuid::Uuid) -> Self {
        Self::Strategy(StrategyError::ExecutionFailed {
            message: message.into(),
            strategy_id,
        })
    }
    
    /// 创建风险错误
    pub fn risk_error(
        limit_type: impl Into<String>,
        limit: rust_decimal::Decimal,
        current: rust_decimal::Decimal,
    ) -> Self {
        Self::Risk(RiskError::LimitExceeded {
            limit_type: limit_type.into(),
            limit,
            current,
        })
    }
    
    /// 创建验证错误
    pub fn validation_error(field: impl Into<String>, message: impl Into<String>) -> Self {
        Self::Validation(ValidationError::FieldValidation {
            field: field.into(),
            message: message.into(),
            value: None,
        })
    }
    
    /// 创建数据库错误
    pub fn database_error(message: impl Into<String>) -> Self {
        Self::Database(DatabaseError::QueryFailed {
            query: "unknown".to_string(),
            message: message.into(),
        })
    }
    
    /// 创建网络错误
    pub fn network_error(endpoint: impl Into<String>, timeout_ms: u64) -> Self {
        Self::Network(NetworkError::Timeout {
            endpoint: endpoint.into(),
            timeout_ms,
        })
    }

    /// 创建配置错误
    pub fn config_error(message: impl Into<String>) -> Self {
        Self::Configuration(ConfigurationError::InvalidValue {
            key: "unknown".to_string(),
            value: "unknown".to_string(),
            message: message.into(),
        })
    }

    /// 创建内部错误（通用）
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self::Database(DatabaseError::QueryFailed {
            query: "internal".to_string(),
            message: message.into(),
        })
    }

    /// 创建参数验证错误
    pub fn invalid_parameter(message: impl Into<String>) -> Self {
        Self::validation_error("parameter", message)
    }
}

// 为了向后兼容，添加 From 实现
impl From<validator::ValidationErrors> for SigmaXError {
    fn from(errors: validator::ValidationErrors) -> Self {
        let error_messages: Vec<String> = errors
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                errors.iter().map(move |error| {
                    let message = error.message
                        .as_ref()
                        .map(|m| m.to_string())
                        .unwrap_or_else(|| "Validation failed".to_string());
                    format!("{}: {}", field, message)
                })
            })
            .collect();

        Self::validation_error("validation", error_messages.join("; "))
    }
}

impl From<serde_json::Error> for SigmaXError {
    fn from(error: serde_json::Error) -> Self {
        Self::Serialization(format!("JSON serialization error: {}", error))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_sigmax_error_domain_error_impl() {
        let trading_error = SigmaXError::Trading(TradingError::InvalidOrder {
            message: "Test order error".to_string(),
            order_id: None,
        });
        
        assert_eq!(trading_error.category(), ErrorCategory::Business);
        assert_eq!(trading_error.severity(), ErrorSeverity::Error);
        assert_eq!(trading_error.error_code(), "TRADING_INVALID_ORDER");
    }
    
    #[test]
    fn test_sigmax_error_convenience_constructors() {
        let trading_error = SigmaXError::trading_error("Invalid amount", None);
        assert!(matches!(trading_error, SigmaXError::Trading(_)));
        
        let strategy_error = SigmaXError::strategy_error("Execution failed", uuid::Uuid::new_v4());
        assert!(matches!(strategy_error, SigmaXError::Strategy(_)));
    }
    
    #[test]
    fn test_error_metadata() {
        let risk_error = SigmaXError::risk_error(
            "position_limit",
            rust_decimal::Decimal::new(1000, 0),
            rust_decimal::Decimal::new(1500, 0),
        );
        
        let metadata = risk_error.metadata();
        assert!(metadata.contains_key("limit_type"));
        assert!(metadata.contains_key("limit_value"));
        assert!(metadata.contains_key("current_value"));
    }
}
