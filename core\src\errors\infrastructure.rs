//! 基础设施错误定义
//! 
//! 遵循关注点分离原则：基础设施错误与业务错误完全分离
//! 专注于技术层面的错误处理

use super::domain::{DomainError, ErrorCategory, ErrorSeverity};
use thiserror::Error;
use std::collections::HashMap;

/// 数据库错误 - 专注于数据访问层
#[derive(Error, Debug, Clone)]
pub enum DatabaseError {
    #[error("Connection failed: {message}")]
    ConnectionFailed { 
        message: String,
        database_url: Option<String>,
    },
    
    #[error("Query execution failed: {query} - {message}")]
    QueryFailed { 
        query: String,
        message: String,
    },
    
    #[error("Transaction failed: {message}")]
    TransactionFailed { 
        message: String,
    },
    
    #[error("Connection pool exhausted: active {active}, max {max}")]
    PoolExhausted { 
        active: u32,
        max: u32,
    },
    
    #[error("Migration failed: {version} - {message}")]
    MigrationFailed { 
        version: String,
        message: String,
    },
}

impl DomainError for DatabaseError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Infrastructure
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            DatabaseError::ConnectionFailed { .. } => ErrorSeverity::Critical,
            DatabaseError::QueryFailed { .. } => ErrorSeverity::Error,
            DatabaseError::TransactionFailed { .. } => ErrorSeverity::Error,
            DatabaseError::PoolExhausted { .. } => ErrorSeverity::Warning,
            DatabaseError::MigrationFailed { .. } => ErrorSeverity::Critical,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            DatabaseError::ConnectionFailed { .. } => "DB_CONNECTION_FAILED",
            DatabaseError::QueryFailed { .. } => "DB_QUERY_FAILED",
            DatabaseError::TransactionFailed { .. } => "DB_TRANSACTION_FAILED",
            DatabaseError::PoolExhausted { .. } => "DB_POOL_EXHAUSTED",
            DatabaseError::MigrationFailed { .. } => "DB_MIGRATION_FAILED",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            DatabaseError::ConnectionFailed { database_url, .. } => {
                if let Some(url) = database_url {
                    meta.insert("database_url".to_string(), url.clone());
                }
            },
            DatabaseError::QueryFailed { query, .. } => {
                meta.insert("sql_query".to_string(), query.clone());
            },
            DatabaseError::PoolExhausted { active, max } => {
                meta.insert("active_connections".to_string(), active.to_string());
                meta.insert("max_connections".to_string(), max.to_string());
            },
            DatabaseError::MigrationFailed { version, .. } => {
                meta.insert("migration_version".to_string(), version.clone());
            },
            _ => {},
        }
        meta
    }
}

/// 网络错误 - 专注于网络通信层
#[derive(Error, Debug, Clone)]
pub enum NetworkError {
    #[error("Connection timeout: {endpoint} after {timeout_ms}ms")]
    Timeout { 
        endpoint: String,
        timeout_ms: u64,
    },
    
    #[error("Connection refused: {endpoint}")]
    ConnectionRefused { 
        endpoint: String,
    },
    
    #[error("DNS resolution failed: {hostname}")]
    DnsResolutionFailed { 
        hostname: String,
    },
    
    #[error("SSL/TLS error: {message}")]
    TlsError { 
        message: String,
    },
    
    #[error("HTTP error: {status_code} - {message}")]
    HttpError { 
        status_code: u16,
        message: String,
        endpoint: String,
    },
}

impl DomainError for NetworkError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Infrastructure
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            NetworkError::Timeout { .. } => ErrorSeverity::Warning,
            NetworkError::ConnectionRefused { .. } => ErrorSeverity::Warning,
            NetworkError::DnsResolutionFailed { .. } => ErrorSeverity::Error,
            NetworkError::TlsError { .. } => ErrorSeverity::Error,
            NetworkError::HttpError { status_code, .. } => {
                match *status_code {
                    400..=499 => ErrorSeverity::Error,
                    500..=599 => ErrorSeverity::Warning,
                    _ => ErrorSeverity::Error,
                }
            },
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            NetworkError::Timeout { .. } => "NET_TIMEOUT",
            NetworkError::ConnectionRefused { .. } => "NET_CONNECTION_REFUSED",
            NetworkError::DnsResolutionFailed { .. } => "NET_DNS_FAILED",
            NetworkError::TlsError { .. } => "NET_TLS_ERROR",
            NetworkError::HttpError { .. } => "NET_HTTP_ERROR",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            NetworkError::Timeout { endpoint, timeout_ms } => {
                meta.insert("endpoint".to_string(), endpoint.clone());
                meta.insert("timeout_ms".to_string(), timeout_ms.to_string());
            },
            NetworkError::ConnectionRefused { endpoint } => {
                meta.insert("endpoint".to_string(), endpoint.clone());
            },
            NetworkError::DnsResolutionFailed { hostname } => {
                meta.insert("hostname".to_string(), hostname.clone());
            },
            NetworkError::HttpError { status_code, endpoint, .. } => {
                meta.insert("status_code".to_string(), status_code.to_string());
                meta.insert("endpoint".to_string(), endpoint.clone());
            },
            _ => {},
        }
        meta
    }
}

/// 缓存错误 - 专注于缓存层
#[derive(Error, Debug, Clone)]
pub enum CacheError {
    #[error("Cache connection failed: {cache_type} - {message}")]
    ConnectionFailed { 
        cache_type: String,
        message: String,
    },
    
    #[error("Cache operation failed: {operation} on key '{key}' - {message}")]
    OperationFailed { 
        operation: String,
        key: String,
        message: String,
    },
    
    #[error("Cache serialization failed: {message}")]
    SerializationFailed { 
        message: String,
        data_type: String,
    },
    
    #[error("Cache eviction policy violated: {message}")]
    EvictionPolicyViolated { 
        message: String,
    },
}

impl DomainError for CacheError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Infrastructure
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            CacheError::ConnectionFailed { .. } => ErrorSeverity::Warning,
            CacheError::OperationFailed { .. } => ErrorSeverity::Warning,
            CacheError::SerializationFailed { .. } => ErrorSeverity::Error,
            CacheError::EvictionPolicyViolated { .. } => ErrorSeverity::Warning,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            CacheError::ConnectionFailed { .. } => "CACHE_CONNECTION_FAILED",
            CacheError::OperationFailed { .. } => "CACHE_OPERATION_FAILED",
            CacheError::SerializationFailed { .. } => "CACHE_SERIALIZATION_FAILED",
            CacheError::EvictionPolicyViolated { .. } => "CACHE_EVICTION_VIOLATED",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            CacheError::ConnectionFailed { cache_type, .. } => {
                meta.insert("cache_type".to_string(), cache_type.clone());
            },
            CacheError::OperationFailed { operation, key, .. } => {
                meta.insert("operation".to_string(), operation.clone());
                meta.insert("cache_key".to_string(), key.clone());
            },
            CacheError::SerializationFailed { data_type, .. } => {
                meta.insert("data_type".to_string(), data_type.clone());
            },
            _ => {},
        }
        meta
    }
}

/// 配置错误 - 专注于配置管理层
#[derive(Error, Debug, Clone)]
pub enum ConfigurationError {
    #[error("Configuration file not found: {file_path}")]
    FileNotFound { 
        file_path: String,
    },
    
    #[error("Configuration parsing failed: {file_path} - {message}")]
    ParsingFailed { 
        file_path: String,
        message: String,
    },
    
    #[error("Required configuration missing: {key}")]
    RequiredKeyMissing { 
        key: String,
    },
    
    #[error("Invalid configuration value: {key} = {value} - {message}")]
    InvalidValue { 
        key: String,
        value: String,
        message: String,
    },
}

impl DomainError for ConfigurationError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::Infrastructure
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            ConfigurationError::FileNotFound { .. } => ErrorSeverity::Critical,
            ConfigurationError::ParsingFailed { .. } => ErrorSeverity::Critical,
            ConfigurationError::RequiredKeyMissing { .. } => ErrorSeverity::Critical,
            ConfigurationError::InvalidValue { .. } => ErrorSeverity::Error,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            ConfigurationError::FileNotFound { .. } => "CONFIG_FILE_NOT_FOUND",
            ConfigurationError::ParsingFailed { .. } => "CONFIG_PARSING_FAILED",
            ConfigurationError::RequiredKeyMissing { .. } => "CONFIG_REQUIRED_KEY_MISSING",
            ConfigurationError::InvalidValue { .. } => "CONFIG_INVALID_VALUE",
        }
    }
    
    fn is_retryable(&self) -> bool {
        false // 配置错误通常不可重试
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            ConfigurationError::FileNotFound { file_path } => {
                meta.insert("file_path".to_string(), file_path.clone());
            },
            ConfigurationError::ParsingFailed { file_path, .. } => {
                meta.insert("file_path".to_string(), file_path.clone());
            },
            ConfigurationError::RequiredKeyMissing { key } => {
                meta.insert("config_key".to_string(), key.clone());
            },
            ConfigurationError::InvalidValue { key, value, .. } => {
                meta.insert("config_key".to_string(), key.clone());
                meta.insert("config_value".to_string(), value.clone());
            },
        }
        meta
    }
}
