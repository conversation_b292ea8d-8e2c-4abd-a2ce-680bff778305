#!/usr/bin/env python3
"""
批量修复数据库模块中的错误类型问题
将 SigmaXError::Database(format!(...)) 替换为 SigmaXError::database_error(...)
"""

import os
import re
import glob

def fix_database_errors_in_file(file_path):
    """修复单个文件中的数据库错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换 SigmaXError::Database(format!(...)) 模式
        pattern = r'SigmaXError::Database\(format!\("([^"]+)"([^)]*)\)\)'
        
        def replace_func(match):
            message = match.group(1)
            args = match.group(2)
            if args.strip():
                return f'SigmaXError::database_error(format!("{message}"{args}))'
            else:
                return f'SigmaXError::database_error("{message}")'
        
        new_content = re.sub(pattern, replace_func, content)
        
        # 查找并替换简单的字符串模式
        simple_pattern = r'SigmaXError::Database\("([^"]+)"\)'
        new_content = re.sub(simple_pattern, r'SigmaXError::database_error("\1")', new_content)

        # 查找并替换 .to_string() 模式
        to_string_pattern = r'SigmaXError::Database\(([^)]+)\.to_string\(\)\)'
        new_content = re.sub(to_string_pattern, r'SigmaXError::database_error(\1.to_string())', new_content)

        # 查找并替换 return Err(SigmaXError::Database(...)) 模式
        return_pattern = r'return Err\(SigmaXError::Database\("([^"]+)"\.to_string\(\)\)\)'
        new_content = re.sub(return_pattern, r'return Err(SigmaXError::database_error("\1"))', new_content)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Fixed: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Rust 文件
    patterns = [
        "database/src/**/*.rs",
        "services/src/**/*.rs"
    ]
    
    fixed_count = 0
    total_count = 0
    
    for pattern in patterns:
        for file_path in glob.glob(pattern, recursive=True):
            total_count += 1
            if fix_database_errors_in_file(file_path):
                fixed_count += 1
    
    print(f"\n修复完成: {fixed_count}/{total_count} 个文件被修改")

if __name__ == "__main__":
    main()
