//! Web路由定义
//!
//! 本文件聚合了SigmaX交易系统的所有API路由定义
//!
//! ## API模块组织结构
//!
//! ### 核心功能模块
//! - **系统状态**: 健康检查和系统监控
//! - **引擎管理**: 交易引擎的生命周期管理
//! - **策略管理**: 策略的CRUD操作和执行控制
//! - **风险管理**: 风险规则和参数配置
//! - **回测系统**: 回测数据管理和结果分析
//! - **实时通信**: WebSocket连接和事件推送
//!
//! ### API版本说明
//! - **v1**: 基础API，保持向后兼容
//! - **v1**: 增强API，提供更丰富的功能和验证

use axum::{
    routing::{get, post, put, delete},
    Router,
};
use tower_http::services::ServeDir;
use crate::{
    handlers::{
        basic_handlers::{
            get_health, get_system_status, get_basic_orders, get_basic_trades
        },
        backtest_data::{
            get_backtest_files, get_backtest_candles, get_backtest_stats, validate_backtest_file
        },
        backtest_results::{
            set_backtest_config, get_backtest_config, check_backtest_config_status, get_backtest_progress,
            get_backtest_result, get_backtest_trades, get_backtest_portfolio, get_backtest_report
        },
        strategy_handlers::{
            create_strategy, get_strategies, get_strategy, update_strategy, delete_strategy
            // 🗑️ 已移除: start_strategy, stop_strategy, get_strategy_status
        },
        strategy_validation_handlers::{
            validate_strategy_config, get_supported_strategies,
            get_strategy_config_template, preview_strategy_config
        },
        // 注意：风险管理功能已迁移到统一风险管理API (/api/risk/*)
        // 旧的 risk_config_handlers 和部分 risk_handlers 已被移除
        // 第二阶段：投资组合与订单管理API处理器
        portfolio_handlers::{
            get_portfolios, create_portfolio, get_portfolio, update_portfolio, delete_portfolio,
            get_portfolio_balances, get_portfolio_performance, rebalance_portfolio
        },
        order_handlers::{
            get_order, update_order, cancel_order,
            get_order_status, execute_order, get_order_history, batch_create_orders, get_order_statistics
        },
        position_handlers::{
            get_positions, get_position_by_symbol, close_position,
            get_position_summary, get_pnl_details, hedge_positions
        },
        // 第三阶段：数据管理与性能监控API处理器
        data_management_handlers::{
            get_data_providers, create_data_provider, update_data_provider, delete_data_provider,
            validate_data_quality, get_data_quality_report, clean_data, check_data_integrity,
            import_data, export_data, create_data_backup, restore_data
        },
        performance_monitoring_handlers::{
            get_system_metrics, get_system_health, get_resource_usage, get_performance_stats,
            get_monitoring_alerts, create_monitoring_alert_rule, update_monitoring_alert_rule,
            delete_monitoring_alert_rule, run_performance_benchmark, get_system_logs
        },
        cache_management_handlers::{
            get_cache_stats, clear_cache, warm_cache, get_cache_keys, delete_cache_key, get_cache_health
        },
        database_management_handlers::{
            get_database_status, execute_database_migration, get_connection_pool_status, optimize_database
        },
        execution_handlers::{
            get_strategy_execution_status, get_strategy_real_time_performance,
            get_running_strategies, get_all_execution_status, emergency_stop_all_strategies,
            push_market_data
        },
        // 第四阶段：交易所集成与报告生成API处理器
        exchange_handlers::{
            get_exchanges, get_exchange_info, connect_exchange, disconnect_exchange,
            get_exchange_status, get_exchange_health, test_exchange_connection,
            get_all_exchanges_status, get_exchange_balances, get_exchange_orders,
            get_exchange_trades, get_exchange_symbols
        },
        market_data_handlers::{
            get_ticker, get_orderbook, get_recent_trades, get_candles,
            get_volume_data, get_volatility_data, get_correlation_analysis, get_market_sentiment
        },
        report_handlers::{
            get_performance_report, get_risk_report, get_trades_report, get_portfolio_report,
            generate_custom_report, get_report, delete_report, get_report_templates,
            export_pdf_report, export_excel_report
        },
        system_config_handlers::{
            // 系统级配置管理
            get_system_config, update_system_config,
            get_api_config, update_api_config,
            get_cache_config, update_cache_config,
            get_monitoring_config, update_monitoring_config,
        },
        strategy_template_handlers::{
            get_strategy_templates, get_strategy_template_by_id, validate_strategy_template_config,
            get_recommended_config
        },
        websocket_handlers,
        realtime_handlers::{
            start_realtime_monitoring, stop_realtime_monitoring, get_realtime_monitoring_status,
            connect_backtest_progress, disconnect_backtest_progress,
            realtime_websocket_stats
        },
    },
    engine_handlers::{
        create_engine, start_engine, stop_engine, get_engine_status,
        get_engine, get_engines, delete_engine, validate_engine_config,
    },
    websocket::WebSocketServer,
    state::AppState,
};

/// 创建API路由
///
/// 聚合所有API端点，按功能模块组织
pub fn create_api_routes() -> Router<AppState> {
    Router::new()
        // ============================================================================
        // 系统状态和监控
        // ============================================================================
        .route("/api/v1/health", get(get_health))
        .route("/api/v1/status", get(get_system_status))
        .route("/api/v1/orders", get(get_basic_orders))
        .route("/api/v1/trades", get(get_basic_trades))

        // ============================================================================
        // 策略管理 (v1 API - 基础功能)
        // ============================================================================
        .route("/api/v1/strategies", get(get_strategies).post(create_strategy))
        .route("/api/v1/strategies/:id", get(get_strategy).put(update_strategy).delete(delete_strategy))
        // 🗑️ 已移除策略级别的start/stop/status API - 改用引擎级别控制
        // .route("/api/v1/strategies/validate-config", post(validate_strategy_config)) ///由api/v1/strategies/validate 取代

        // ============================================================================
        // 策略模板管理 (v1 API - 预配置策略模板)
        // ============================================================================
        .route("/api/v1/strategy-templates", get(get_strategy_templates))
        .route("/api/v1/strategy-templates/:template_id", get(get_strategy_template_by_id))
        .route("/api/v1/strategy-templates/:template_id/validate", post(validate_strategy_template_config))
        .route("/api/v1/strategy-templates/:template_id/recommended-config", get(get_recommended_config))

        // ============================================================================
        // 策略管理 (v1 API - 增强功能)
        // ============================================================================
        .route("/api/v1/strategies/validate", post(validate_strategy_config))
        .route("/api/v1/strategies/supported", get(get_supported_strategies))
        .route("/api/v1/strategies/:strategy_type/template", get(get_strategy_config_template))
        .route("/api/v1/strategies/preview", post(preview_strategy_config))

        // ============================================================================
        // 策略执行管理
        // ============================================================================
        .route("/api/v1/strategies/:id/execution/status", get(get_strategy_execution_status))
        .route("/api/v1/strategies/:id/execution/performance", get(get_strategy_real_time_performance))
        .route("/api/v1/strategies/execution/running", get(get_running_strategies))
        .route("/api/v1/strategies/execution/status", get(get_all_execution_status))
        .route("/api/v1/strategies/execution/emergency-stop", post(emergency_stop_all_strategies))
        .route("/api/v1/market-data/push", post(push_market_data))

        // ============================================================================
        // 统一风险管理API - 企业级风险管理解决方案
        // ============================================================================
        .nest("/api/risk", crate::risk_api::create_unified_risk_api())

        // ============================================================================
        // 投资组合与订单管理 (v1 API) - 第二阶段实施
        // ============================================================================

        // 投资组合管理 (8个端点)
        .route("/api/v1/portfolios", get(get_portfolios).post(create_portfolio))
        .route("/api/v1/portfolios/:id", get(get_portfolio).put(update_portfolio).delete(delete_portfolio))
        .route("/api/v1/portfolios/:id/balances", get(get_portfolio_balances))
        .route("/api/v1/portfolios/:id/performance", get(get_portfolio_performance))
        .route("/api/v1/portfolios/:id/rebalance", post(rebalance_portfolio))

        // 订单管理 (10个端点)
        // .route("/api/v1/orders", get(get_orders).post(create_order))
        .route("/api/v1/orders/:id", get(get_order).put(update_order).delete(cancel_order))
        .route("/api/v1/orders/:id/status", get(get_order_status))
        .route("/api/v1/orders/:id/execute", post(execute_order))
        .route("/api/v1/orders/history", get(get_order_history))
        .route("/api/v1/orders/batch", post(batch_create_orders))
        .route("/api/v1/orders/statistics", get(get_order_statistics))

        // 持仓管理 (6个端点)
        .route("/api/v1/positions", get(get_positions))
        .route("/api/v1/positions/:symbol", get(get_position_by_symbol))
        .route("/api/v1/positions/:symbol/close", post(close_position))
        .route("/api/v1/positions/summary", get(get_position_summary))
        .route("/api/v1/positions/pnl", get(get_pnl_details))
        .route("/api/v1/positions/hedge", post(hedge_positions))

        // ============================================================================
        // 数据管理与性能监控 (v1 API) - 第三阶段实施
        // ============================================================================

        // 数据管理 (12个端点)
        .route("/api/v1/data/providers", get(get_data_providers).post(create_data_provider))
        .route("/api/v1/data/providers/:id", put(update_data_provider).delete(delete_data_provider))
        .route("/api/v1/data/validate", post(validate_data_quality))
        .route("/api/v1/data/quality/report", get(get_data_quality_report))
        .route("/api/v1/data/clean", post(clean_data))
        .route("/api/v1/data/integrity", get(check_data_integrity))
        .route("/api/v1/data/import", post(import_data))
        .route("/api/v1/data/export", get(export_data))
        .route("/api/v1/data/backup", post(create_data_backup))
        .route("/api/v1/data/restore", post(restore_data))

        // 性能监控 (10个端点)
        .route("/api/v1/monitoring/metrics", get(get_system_metrics))
        .route("/api/v1/monitoring/health", get(get_system_health))
        .route("/api/v1/monitoring/resources", get(get_resource_usage))
        .route("/api/v1/monitoring/performance", get(get_performance_stats))
        .route("/api/v1/monitoring/alerts", get(get_monitoring_alerts).post(create_monitoring_alert_rule))
        .route("/api/v1/monitoring/alerts/:id", put(update_monitoring_alert_rule).delete(delete_monitoring_alert_rule))
        .route("/api/v1/monitoring/benchmark", post(run_performance_benchmark))
        .route("/api/v1/monitoring/logs", get(get_system_logs))

        // 缓存管理 (6个端点)
        .route("/api/v1/cache/stats", get(get_cache_stats))
        .route("/api/v1/cache/clear", post(clear_cache))
        .route("/api/v1/cache/warm", post(warm_cache))
        .route("/api/v1/cache/keys", get(get_cache_keys))
        .route("/api/v1/cache/keys/:key", delete(delete_cache_key))
        .route("/api/v1/cache/health", get(get_cache_health))

        // 数据库管理 (4个端点)
        .route("/api/v1/database/status", get(get_database_status))
        .route("/api/v1/database/migrate", post(execute_database_migration))
        .route("/api/v1/database/connections", get(get_connection_pool_status))
        .route("/api/v1/database/optimize", post(optimize_database))

        // ============================================================================
        // 交易所集成与报告生成 (v1 API) - 第四阶段实施
        // ============================================================================

        // 交易所管理 (12个端点) ✅
        .route("/api/v1/exchanges", get(get_exchanges))
        .route("/api/v1/exchanges/:id/info", get(get_exchange_info))
        .route("/api/v1/exchanges/:id/connect", post(connect_exchange))
        .route("/api/v1/exchanges/:id/disconnect", delete(disconnect_exchange))
        .route("/api/v1/exchanges/:id/status", get(get_exchange_status))
        .route("/api/v1/exchanges/:id/health", get(get_exchange_health))
        .route("/api/v1/exchanges/:id/test", post(test_exchange_connection))
        .route("/api/v1/exchanges/status/all", get(get_all_exchanges_status))
        .route("/api/v1/exchanges/:id/balances", get(get_exchange_balances))
        .route("/api/v1/exchanges/:id/orders", get(get_exchange_orders))
        .route("/api/v1/exchanges/:id/trades", get(get_exchange_trades))
        .route("/api/v1/exchanges/:id/symbols", get(get_exchange_symbols))

        // 市场数据 (8个端点) ✅
        .route("/api/v1/market/ticker/:symbol", get(get_ticker))
        .route("/api/v1/market/orderbook/:symbol", get(get_orderbook))
        .route("/api/v1/market/trades/:symbol", get(get_recent_trades))
        .route("/api/v1/market/candles/:symbol", get(get_candles))
        .route("/api/v1/market/volume/:symbol", get(get_volume_data))
        .route("/api/v1/market/volatility/:symbol", get(get_volatility_data))
        .route("/api/v1/market/correlation", get(get_correlation_analysis))
        .route("/api/v1/market/sentiment", get(get_market_sentiment))

        // 报告生成 (9个端点) ✅
        .route("/api/v1/reports/performance", get(get_performance_report))
        .route("/api/v1/reports/risk", get(get_risk_report))
        .route("/api/v1/reports/trades", get(get_trades_report))
        .route("/api/v1/reports/portfolio", get(get_portfolio_report))
        .route("/api/v1/reports/generate", post(generate_custom_report))
        .route("/api/v1/reports/:id", get(get_report).delete(delete_report))
        .route("/api/v1/reports/templates", get(get_report_templates))
        .route("/api/v1/reports/:id/export/pdf", get(export_pdf_report))
        .route("/api/v1/reports/:id/export/excel", get(export_excel_report))

        // ============================================================================
        // 系统级配置管理 API
        // ============================================================================

        // 系统级强类型配置管理
        .route("/api/v1/config/system", get(get_system_config).put(update_system_config))
        .route("/api/v1/config/api", get(get_api_config).put(update_api_config))
        .route("/api/v1/config/cache", get(get_cache_config).put(update_cache_config))
        .route("/api/v1/config/monitoring", get(get_monitoring_config).put(update_monitoring_config))

        // ============================================================================
        // 回测数据管理
        // ============================================================================
        .route("/api/v1/backtest/files", get(get_backtest_files))
        .route("/api/v1/backtest/files/:filename", get(get_backtest_candles))
        .route("/api/v1/backtest/files/:filename/validate", get(validate_backtest_file))
        .route("/api/v1/backtest/stats", get(get_backtest_stats))

        // ============================================================================
        // 引擎管理
        // ============================================================================
        .route("/api/v1/engines", get(get_engines).post(create_engine))
        .route("/api/v1/engines/:id", get(get_engine).delete(delete_engine))
        .route("/api/v1/engines/:id/start", post(start_engine))
        .route("/api/v1/engines/:id/stop", post(stop_engine))
        .route("/api/v1/engines/:id/status", get(get_engine_status))
        .route("/api/v1/engines/validate-config", post(validate_engine_config))

        // ============================================================================
        // 回测配置和结果管理
        // ============================================================================
        .route("/api/v1/engines/:id/backtest/config", post(set_backtest_config).get(get_backtest_config))
        .route("/api/v1/engines/:id/backtest/config/status", get(check_backtest_config_status))
        .route("/api/v1/engines/:id/backtest/progress", get(get_backtest_progress))
        .route("/api/v1/engines/:id/backtest/result", get(get_backtest_result))
        .route("/api/v1/engines/:id/backtest/trades", get(get_backtest_trades))
        .route("/api/v1/engines/:id/backtest/portfolio", get(get_backtest_portfolio))
        .route("/api/v1/engines/:id/backtest/report", get(get_backtest_report))

        // ============================================================================
        // 实时通信 - WebSocket
        // ============================================================================
        .route("/ws", get(WebSocketServer::handle_websocket_upgrade))

        // ============================================================================
        // WebSocket管理API (v1) 🆕
        // ============================================================================
        .route("/api/v1/websocket/stats", get(websocket_handlers::get_websocket_stats))
        .route("/api/v1/websocket/connections", get(websocket_handlers::get_websocket_connections))
        .route("/api/v1/websocket/connections/:id", get(websocket_handlers::get_websocket_connection))
        .route("/api/v1/websocket/connections/:id", delete(websocket_handlers::disconnect_websocket_connection))
        .route("/api/v1/websocket/broadcast", post(websocket_handlers::broadcast_websocket_message))
        .route("/api/v1/websocket/config", get(websocket_handlers::get_websocket_config))
        .route("/api/v1/websocket/cleanup", post(websocket_handlers::cleanup_websocket_connections))

        // ============================================================================
        // 实时进度监控API (v1) 🆕
        // ============================================================================
        .route("/api/v1/realtime/monitoring/start", post(start_realtime_monitoring))
        .route("/api/v1/realtime/monitoring/stop", post(stop_realtime_monitoring))
        .route("/api/v1/realtime/monitoring/status", get(get_realtime_monitoring_status))
        .route("/api/v1/realtime/backtest/:engine_id/connect", post(connect_backtest_progress))
        .route("/api/v1/realtime/backtest/:engine_id/disconnect", post(disconnect_backtest_progress))
        .route("/api/v1/realtime/websocket/stats", get(realtime_websocket_stats))
}

/// 创建完整路由（包含静态文件等）
///
/// 合并API路由和静态文件服务
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .merge(create_api_routes())
        // ============================================================================
        // 静态文件服务 - 前端资源
        // ============================================================================
        .nest_service("/", ServeDir::new("web/static"))
}

// ============================================================================
// API端点统计信息
// ============================================================================

/// API端点统计
///
/// 当前聚合的API端点总数: 146个 (第四阶段实施完成)
///
/// ## 按模块分类:
/// - 系统状态: 1个端点
/// - 策略管理(v1): 7个端点
/// - 策略管理(v1): 4个端点
/// - 风险配置(v1): 4个端点
/// - 策略执行: 6个端点
/// - 风控管理(v1): 4个端点
/// - **风险管理(v1)**: 10个端点 ✅
/// - **投资组合管理(v1)**: 8个端点 ✅
/// - **订单管理(v1)**: 10个端点 ✅
/// - **持仓管理(v1)**: 6个端点 ✅
/// - **数据管理(v1)**: 12个端点 ✅
/// - **性能监控(v1)**: 10个端点 ✅
/// - **缓存管理(v1)**: 6个端点 ✅
/// - **数据库管理(v1)**: 4个端点 ✅
/// - **交易所管理(v1)**: 12个端点 🆕
/// - **市场数据(v1)**: 8个端点 🆕
/// - **报告生成(v1)**: 10个端点 🆕
/// - **系统配置(v1)**: 8个端点 🆕
/// - 回测数据: 4个端点
/// - 引擎管理: 6个端点
/// - 回测结果: 6个端点
/// - WebSocket: 1个端点
/// - 静态文件: 1个服务
///
/// ## 版本说明:
/// - v1 API: 基础功能，保持向后兼容
/// - v1 API: 增强功能，提供更丰富的验证和配置选项
///
/// ## 实施进度:
/// - ✅ 第一阶段: 风险管理API (10个端点) - 已完成
/// - ✅ 第二阶段: 投资组合与订单管理API (24个端点) - 已完成
/// - ✅ 第三阶段: 数据管理与性能监控API (32个端点) - 已完成
/// - ✅ 第四阶段: 交易所集成与报告生成API (38个端点) - 已完成
///
/// ## 测试覆盖:
/// 所有API端点都应该通过 `scripts/test_complete_api_coverage.sh` 进行测试
#[allow(dead_code)]
const API_ENDPOINTS_COUNT: usize = 146;
