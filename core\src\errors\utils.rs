//! 错误处理工具
//!
//! 提供统一的错误处理工具和扩展方法

use super::unified::{UnifiedError, UnifiedResult, ErrorContext, EnhancedError};
use std::collections::HashMap;

/// 错误上下文构建器
pub struct ErrorContextBuilder {
    module: String,
    function: String,
    file: String,
    line: u32,
    user_id: Option<uuid::Uuid>,
    session_id: Option<String>,
    request_id: Option<String>,
    metadata: HashMap<String, String>,
}

impl ErrorContextBuilder {
    /// 创建新的错误上下文构建器
    pub fn new(module: &str, function: &str, file: &str, line: u32) -> Self {
        Self {
            module: module.to_string(),
            function: function.to_string(),
            file: file.to_string(),
            line,
            user_id: None,
            session_id: None,
            request_id: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 设置用户ID
    pub fn user_id(mut self, user_id: uuid::Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }
    
    /// 设置会话ID
    pub fn session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }
    
    /// 设置请求ID
    pub fn request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
    
    /// 添加元数据
    pub fn metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
    
    /// 构建错误上下文
    pub fn build(self) -> ErrorContext {
        ErrorContext {
            module: self.module,
            function: self.function,
            file: self.file,
            line: self.line,
            user_id: self.user_id,
            session_id: self.session_id,
            request_id: self.request_id,
            metadata: self.metadata,
        }
    }
}

/// 错误上下文宏，自动获取当前位置信息
#[macro_export]
macro_rules! error_context {
    ($module:expr) => {
        $crate::errors::utils::ErrorContextBuilder::new(
            $module,
            std::any::type_name::<fn()>(),
            file!(),
            line!()
        )
    };
    ($module:expr, $function:expr) => {
        $crate::errors::utils::ErrorContextBuilder::new(
            $module,
            $function,
            file!(),
            line!()
        )
    };
}

/// Result扩展trait，提供错误处理工具方法
pub trait ResultExt<T> {
    /// 添加错误上下文
    fn with_context(self, context: ErrorContext) -> Result<T, EnhancedError>;
    
    /// 使用构建器添加错误上下文
    fn with_context_builder<F>(self, f: F) -> Result<T, EnhancedError>
    where
        F: FnOnce() -> ErrorContext;
    
    /// 记录错误并继续
    fn log_error(self, context: &str) -> Option<T>;
    
    /// 忽略错误，返回默认值
    fn ignore_error(self, default: T) -> T;
    
    /// 转换错误类型
    fn map_error<F>(self, f: F) -> UnifiedResult<T>
    where
        F: FnOnce(UnifiedError) -> UnifiedError;
}

impl<T> ResultExt<T> for UnifiedResult<T> {
    fn with_context(self, context: ErrorContext) -> Result<T, EnhancedError> {
        self.map_err(|err| EnhancedError::new(err, context))
    }
    
    fn with_context_builder<F>(self, f: F) -> Result<T, EnhancedError>
    where
        F: FnOnce() -> ErrorContext,
    {
        self.map_err(|err| EnhancedError::new(err, f()))
    }
    
    fn log_error(self, context: &str) -> Option<T> {
        match self {
            Ok(value) => Some(value),
            Err(e) => {
                tracing::error!("{}: {}", context, e);
                None
            }
        }
    }
    
    fn ignore_error(self, default: T) -> T {
        self.unwrap_or(default)
    }
    
    fn map_error<F>(self, f: F) -> UnifiedResult<T>
    where
        F: FnOnce(UnifiedError) -> UnifiedError,
    {
        self.map_err(f)
    }
}

/// Option扩展trait，提供错误处理工具方法
pub trait OptionExt<T> {
    /// 转换为UnifiedResult，使用指定的错误
    fn ok_or_unified_error(self, error: UnifiedError) -> UnifiedResult<T>;
    
    /// 转换为UnifiedResult，使用错误构建器
    fn ok_or_error_with<F>(self, f: F) -> UnifiedResult<T>
    where
        F: FnOnce() -> UnifiedError;
}

impl<T> OptionExt<T> for Option<T> {
    fn ok_or_unified_error(self, error: UnifiedError) -> UnifiedResult<T> {
        self.ok_or(error)
    }
    
    fn ok_or_error_with<F>(self, f: F) -> UnifiedResult<T>
    where
        F: FnOnce() -> UnifiedError,
    {
        self.ok_or_else(f)
    }
}

/// 重试工具
pub struct RetryConfig {
    /// 最大重试次数
    pub max_attempts: usize,
    /// 初始延迟（毫秒）
    pub initial_delay_ms: u64,
    /// 延迟倍数
    pub backoff_multiplier: f64,
    /// 最大延迟（毫秒）
    pub max_delay_ms: u64,
    /// 是否使用抖动
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_ms: 100,
            backoff_multiplier: 2.0,
            max_delay_ms: 5000,
            jitter: true,
        }
    }
}

/// 重试执行器
pub struct RetryExecutor {
    config: RetryConfig,
}

impl RetryExecutor {
    /// 创建新的重试执行器
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }
    
    /// 同步重试
    pub fn retry<T, F>(&self, mut f: F) -> UnifiedResult<T>
    where
        F: FnMut() -> UnifiedResult<T>,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.config.max_attempts {
            match f() {
                Ok(value) => return Ok(value),
                Err(e) => {
                    if !e.is_retryable() || attempt == self.config.max_attempts {
                        return Err(e);
                    }
                    
                    tracing::warn!("Attempt {} failed: {}", attempt, e);
                    last_error = Some(e);
                    
                    // 计算延迟
                    let delay = self.calculate_delay(attempt);
                    std::thread::sleep(std::time::Duration::from_millis(delay));
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    /// 异步重试
    pub async fn async_retry<T, F, Fut>(&self, mut f: F) -> UnifiedResult<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = UnifiedResult<T>>,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.config.max_attempts {
            match f().await {
                Ok(value) => return Ok(value),
                Err(e) => {
                    if !e.is_retryable() || attempt == self.config.max_attempts {
                        return Err(e);
                    }
                    
                    tracing::warn!("Async attempt {} failed: {}", attempt, e);
                    last_error = Some(e);
                    
                    // 计算延迟
                    let delay = self.calculate_delay(attempt);
                    tokio::time::sleep(std::time::Duration::from_millis(delay)).await;
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    /// 计算延迟时间
    fn calculate_delay(&self, attempt: usize) -> u64 {
        let base_delay = self.config.initial_delay_ms as f64 
            * self.config.backoff_multiplier.powi((attempt - 1) as i32);
        
        let delay = base_delay.min(self.config.max_delay_ms as f64) as u64;
        
        if self.config.jitter {
            // 添加±25%的抖动
            let jitter_range = delay / 4;
            let jitter = fastrand::u64(0..=jitter_range * 2);
            delay.saturating_sub(jitter_range).saturating_add(jitter)
        } else {
            delay
        }
    }
}

/// 批量错误处理器
pub struct BatchErrorHandler {
    errors: Vec<UnifiedError>,
    context: String,
}

impl BatchErrorHandler {
    /// 创建新的批量错误处理器
    pub fn new(context: String) -> Self {
        Self {
            errors: Vec::new(),
            context,
        }
    }
    
    /// 添加错误
    pub fn add_error(&mut self, error: UnifiedError) {
        self.errors.push(error);
    }
    
    /// 检查是否有错误
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }
    
    /// 获取错误数量
    pub fn error_count(&self) -> usize {
        self.errors.len()
    }
    
    /// 生成汇总结果
    pub fn into_result<T>(self, success_value: T) -> UnifiedResult<T> {
        if self.errors.is_empty() {
            Ok(success_value)
        } else {
            use super::unified::{TechnicalError, UnifiedError};
            
            let error_messages: Vec<String> = self.errors
                .into_iter()
                .map(|e| e.to_string())
                .collect();
            
            let mut details = HashMap::new();
            details.insert("error_count".to_string(), error_messages.len().to_string());
            details.insert("errors".to_string(), error_messages.join("; "));
            
            Err(UnifiedError::Technical(TechnicalError::Internal {
                message: format!("{}: {} errors occurred", self.context, error_messages.len()),
                component: "batch_error_handler".to_string(),
                details,
            }))
        }
    }
}
