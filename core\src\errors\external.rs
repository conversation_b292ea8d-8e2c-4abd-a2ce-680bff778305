//! 外部依赖错误定义
//! 
//! 遵循面向接口设计原则：定义清晰的外部错误接口契约
//! 依赖抽象而非具体实现

use super::domain::{DomainError, ErrorCategory, ErrorSeverity};
use thiserror::Error;
use std::collections::HashMap;
use chrono::{DateTime, Utc};

/// 外部API错误接口 - 抽象接口定义
pub trait ExternalApiError: DomainError {
    /// API服务名称
    fn service_name(&self) -> &str;
    
    /// API端点
    fn endpoint(&self) -> Option<&str>;
    
    /// HTTP状态码
    fn status_code(&self) -> Option<u16>;
    
    /// 重试延迟建议（毫秒）
    fn retry_after(&self) -> Option<u64>;
}

/// 交易所API错误 - 具体实现
#[derive(Error, Debug, Clone)]
pub enum ExchangeApiError {
    #[error("Exchange API rate limit exceeded: {exchange} - retry after {retry_after_ms}ms")]
    RateLimitExceeded { 
        exchange: String,
        retry_after_ms: u64,
        endpoint: String,
    },
    
    #[error("Exchange API authentication failed: {exchange} - {message}")]
    AuthenticationFailed { 
        exchange: String,
        message: String,
    },
    
    #[error("Exchange API request failed: {exchange} - {status_code} {message}")]
    RequestFailed { 
        exchange: String,
        status_code: u16,
        message: String,
        endpoint: String,
    },
    
    #[error("Exchange API service unavailable: {exchange} - {message}")]
    ServiceUnavailable { 
        exchange: String,
        message: String,
        estimated_recovery: Option<DateTime<Utc>>,
    },
    
    #[error("Exchange API data format error: {exchange} - {message}")]
    DataFormatError { 
        exchange: String,
        message: String,
        endpoint: String,
    },
}

impl DomainError for ExchangeApiError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::External
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            ExchangeApiError::RateLimitExceeded { .. } => ErrorSeverity::Warning,
            ExchangeApiError::AuthenticationFailed { .. } => ErrorSeverity::Error,
            ExchangeApiError::RequestFailed { status_code, .. } => {
                match *status_code {
                    400..=499 => ErrorSeverity::Error,
                    500..=599 => ErrorSeverity::Warning,
                    _ => ErrorSeverity::Error,
                }
            },
            ExchangeApiError::ServiceUnavailable { .. } => ErrorSeverity::Warning,
            ExchangeApiError::DataFormatError { .. } => ErrorSeverity::Error,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            ExchangeApiError::RateLimitExceeded { .. } => "EXCHANGE_RATE_LIMIT",
            ExchangeApiError::AuthenticationFailed { .. } => "EXCHANGE_AUTH_FAILED",
            ExchangeApiError::RequestFailed { .. } => "EXCHANGE_REQUEST_FAILED",
            ExchangeApiError::ServiceUnavailable { .. } => "EXCHANGE_SERVICE_UNAVAILABLE",
            ExchangeApiError::DataFormatError { .. } => "EXCHANGE_DATA_FORMAT_ERROR",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            ExchangeApiError::RateLimitExceeded { exchange, retry_after_ms, endpoint } => {
                meta.insert("exchange".to_string(), exchange.clone());
                meta.insert("retry_after_ms".to_string(), retry_after_ms.to_string());
                meta.insert("endpoint".to_string(), endpoint.clone());
            },
            ExchangeApiError::AuthenticationFailed { exchange, .. } => {
                meta.insert("exchange".to_string(), exchange.clone());
            },
            ExchangeApiError::RequestFailed { exchange, status_code, endpoint, .. } => {
                meta.insert("exchange".to_string(), exchange.clone());
                meta.insert("status_code".to_string(), status_code.to_string());
                meta.insert("endpoint".to_string(), endpoint.clone());
            },
            ExchangeApiError::ServiceUnavailable { exchange, estimated_recovery, .. } => {
                meta.insert("exchange".to_string(), exchange.clone());
                if let Some(recovery_time) = estimated_recovery {
                    meta.insert("estimated_recovery".to_string(), recovery_time.to_rfc3339());
                }
            },
            ExchangeApiError::DataFormatError { exchange, endpoint, .. } => {
                meta.insert("exchange".to_string(), exchange.clone());
                meta.insert("endpoint".to_string(), endpoint.clone());
            },
        }
        meta
    }
}

impl ExternalApiError for ExchangeApiError {
    fn service_name(&self) -> &str {
        match self {
            ExchangeApiError::RateLimitExceeded { exchange, .. } => exchange,
            ExchangeApiError::AuthenticationFailed { exchange, .. } => exchange,
            ExchangeApiError::RequestFailed { exchange, .. } => exchange,
            ExchangeApiError::ServiceUnavailable { exchange, .. } => exchange,
            ExchangeApiError::DataFormatError { exchange, .. } => exchange,
        }
    }
    
    fn endpoint(&self) -> Option<&str> {
        match self {
            ExchangeApiError::RateLimitExceeded { endpoint, .. } => Some(endpoint),
            ExchangeApiError::RequestFailed { endpoint, .. } => Some(endpoint),
            ExchangeApiError::DataFormatError { endpoint, .. } => Some(endpoint),
            _ => None,
        }
    }
    
    fn status_code(&self) -> Option<u16> {
        match self {
            ExchangeApiError::RequestFailed { status_code, .. } => Some(*status_code),
            _ => None,
        }
    }
    
    fn retry_after(&self) -> Option<u64> {
        match self {
            ExchangeApiError::RateLimitExceeded { retry_after_ms, .. } => Some(*retry_after_ms),
            ExchangeApiError::ServiceUnavailable { .. } => Some(60000), // 1分钟后重试
            _ => None,
        }
    }
}

/// 市场数据提供商错误
#[derive(Error, Debug, Clone)]
pub enum MarketDataError {
    #[error("Market data feed disconnected: {provider}")]
    FeedDisconnected { 
        provider: String,
        last_heartbeat: DateTime<Utc>,
    },
    
    #[error("Market data stale: {provider} - last update {last_update}")]
    DataStale { 
        provider: String,
        last_update: DateTime<Utc>,
        staleness_threshold_ms: u64,
    },
    
    #[error("Market data subscription failed: {provider} - {symbol}")]
    SubscriptionFailed { 
        provider: String,
        symbol: String,
        message: String,
    },
    
    #[error("Market data parsing error: {provider} - {message}")]
    ParsingError { 
        provider: String,
        message: String,
        raw_data: Option<String>,
    },
}

impl DomainError for MarketDataError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::External
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            MarketDataError::FeedDisconnected { .. } => ErrorSeverity::Critical,
            MarketDataError::DataStale { .. } => ErrorSeverity::Warning,
            MarketDataError::SubscriptionFailed { .. } => ErrorSeverity::Error,
            MarketDataError::ParsingError { .. } => ErrorSeverity::Error,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            MarketDataError::FeedDisconnected { .. } => "MARKET_DATA_FEED_DISCONNECTED",
            MarketDataError::DataStale { .. } => "MARKET_DATA_STALE",
            MarketDataError::SubscriptionFailed { .. } => "MARKET_DATA_SUBSCRIPTION_FAILED",
            MarketDataError::ParsingError { .. } => "MARKET_DATA_PARSING_ERROR",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            MarketDataError::FeedDisconnected { provider, last_heartbeat } => {
                meta.insert("provider".to_string(), provider.clone());
                meta.insert("last_heartbeat".to_string(), last_heartbeat.to_rfc3339());
            },
            MarketDataError::DataStale { provider, last_update, staleness_threshold_ms } => {
                meta.insert("provider".to_string(), provider.clone());
                meta.insert("last_update".to_string(), last_update.to_rfc3339());
                meta.insert("staleness_threshold_ms".to_string(), staleness_threshold_ms.to_string());
            },
            MarketDataError::SubscriptionFailed { provider, symbol, .. } => {
                meta.insert("provider".to_string(), provider.clone());
                meta.insert("symbol".to_string(), symbol.clone());
            },
            MarketDataError::ParsingError { provider, raw_data, .. } => {
                meta.insert("provider".to_string(), provider.clone());
                if let Some(data) = raw_data {
                    meta.insert("raw_data".to_string(), data.clone());
                }
            },
        }
        meta
    }
}

impl ExternalApiError for MarketDataError {
    fn service_name(&self) -> &str {
        match self {
            MarketDataError::FeedDisconnected { provider, .. } => provider,
            MarketDataError::DataStale { provider, .. } => provider,
            MarketDataError::SubscriptionFailed { provider, .. } => provider,
            MarketDataError::ParsingError { provider, .. } => provider,
        }
    }
    
    fn endpoint(&self) -> Option<&str> {
        None // 市场数据通常是WebSocket连接，没有REST端点
    }
    
    fn status_code(&self) -> Option<u16> {
        None // WebSocket连接没有HTTP状态码
    }
    
    fn retry_after(&self) -> Option<u64> {
        match self {
            MarketDataError::FeedDisconnected { .. } => Some(5000), // 5秒后重试
            MarketDataError::SubscriptionFailed { .. } => Some(10000), // 10秒后重试
            _ => None,
        }
    }
}

/// 第三方服务错误 - 通用外部服务错误
#[derive(Error, Debug, Clone)]
pub enum ThirdPartyServiceError {
    #[error("Service unavailable: {service_name}")]
    ServiceUnavailable { 
        service_name: String,
        estimated_recovery: Option<DateTime<Utc>>,
    },
    
    #[error("Service timeout: {service_name} - {timeout_ms}ms")]
    Timeout { 
        service_name: String,
        timeout_ms: u64,
    },
    
    #[error("Service quota exceeded: {service_name} - {quota_type}")]
    QuotaExceeded { 
        service_name: String,
        quota_type: String,
        reset_time: Option<DateTime<Utc>>,
    },
    
    #[error("Service configuration error: {service_name} - {message}")]
    ConfigurationError { 
        service_name: String,
        message: String,
    },
}

impl DomainError for ThirdPartyServiceError {
    fn category(&self) -> ErrorCategory {
        ErrorCategory::External
    }
    
    fn severity(&self) -> ErrorSeverity {
        match self {
            ThirdPartyServiceError::ServiceUnavailable { .. } => ErrorSeverity::Warning,
            ThirdPartyServiceError::Timeout { .. } => ErrorSeverity::Warning,
            ThirdPartyServiceError::QuotaExceeded { .. } => ErrorSeverity::Warning,
            ThirdPartyServiceError::ConfigurationError { .. } => ErrorSeverity::Error,
        }
    }
    
    fn error_code(&self) -> &'static str {
        match self {
            ThirdPartyServiceError::ServiceUnavailable { .. } => "THIRD_PARTY_SERVICE_UNAVAILABLE",
            ThirdPartyServiceError::Timeout { .. } => "THIRD_PARTY_TIMEOUT",
            ThirdPartyServiceError::QuotaExceeded { .. } => "THIRD_PARTY_QUOTA_EXCEEDED",
            ThirdPartyServiceError::ConfigurationError { .. } => "THIRD_PARTY_CONFIG_ERROR",
        }
    }
    
    fn metadata(&self) -> HashMap<String, String> {
        let mut meta = HashMap::new();
        match self {
            ThirdPartyServiceError::ServiceUnavailable { service_name, estimated_recovery } => {
                meta.insert("service_name".to_string(), service_name.clone());
                if let Some(recovery_time) = estimated_recovery {
                    meta.insert("estimated_recovery".to_string(), recovery_time.to_rfc3339());
                }
            },
            ThirdPartyServiceError::Timeout { service_name, timeout_ms } => {
                meta.insert("service_name".to_string(), service_name.clone());
                meta.insert("timeout_ms".to_string(), timeout_ms.to_string());
            },
            ThirdPartyServiceError::QuotaExceeded { service_name, quota_type, reset_time } => {
                meta.insert("service_name".to_string(), service_name.clone());
                meta.insert("quota_type".to_string(), quota_type.clone());
                if let Some(reset) = reset_time {
                    meta.insert("quota_reset_time".to_string(), reset.to_rfc3339());
                }
            },
            ThirdPartyServiceError::ConfigurationError { service_name, .. } => {
                meta.insert("service_name".to_string(), service_name.clone());
            },
        }
        meta
    }
}
