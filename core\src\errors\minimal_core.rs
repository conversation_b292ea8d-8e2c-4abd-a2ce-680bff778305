//! 最小错误处理核心
//! 
//! 遵循"做一件事并把它做好"的原则，只提供最基础的错误处理能力

use std::fmt;
use std::error::Error as StdError;

/// 错误严重程度 - 只有必要的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Severity {
    /// 信息性错误，可以继续执行
    Info,
    /// 警告性错误，需要注意但不影响主流程
    Warning,
    /// 错误，影响当前操作但系统可恢复
    Error,
    /// 严重错误，可能影响系统稳定性
    Critical,
}

/// 错误类别 - 简化的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Category {
    /// 业务逻辑错误
    Business,
    /// 技术基础设施错误
    Technical,
    /// 外部依赖错误
    External,
    /// 输入验证错误
    Validation,
}

/// 核心错误特征 - 定义错误的基本契约
pub trait CoreError: StdError + Send + Sync + 'static {
    /// 错误类别
    fn category(&self) -> Category;
    
    /// 错误严重程度
    fn severity(&self) -> Severity;
    
    /// 是否可重试
    fn is_retryable(&self) -> bool {
        matches!(self.category(), Category::External | Category::Technical)
            && matches!(self.severity(), Severity::Warning | Severity::Error)
    }
    
    /// 错误代码 - 用于程序化处理
    fn error_code(&self) -> &'static str;
    
    /// 用户友好的错误消息
    fn user_message(&self) -> String {
        self.to_string()
    }
}

/// 最小错误实现 - 满足80%的使用场景
#[derive(Debug, Clone)]
pub struct SimpleError {
    category: Category,
    severity: Severity,
    code: &'static str,
    message: String,
    source: Option<Box<dyn StdError + Send + Sync>>,
}

impl SimpleError {
    /// 创建新的简单错误
    pub fn new(
        category: Category,
        severity: Severity,
        code: &'static str,
        message: impl Into<String>,
    ) -> Self {
        Self {
            category,
            severity,
            code,
            message: message.into(),
            source: None,
        }
    }
    
    /// 添加错误源
    pub fn with_source(mut self, source: impl StdError + Send + Sync + 'static) -> Self {
        self.source = Some(Box::new(source));
        self
    }
    
    /// 便利构造器 - 业务错误
    pub fn business(code: &'static str, message: impl Into<String>) -> Self {
        Self::new(Category::Business, Severity::Error, code, message)
    }
    
    /// 便利构造器 - 技术错误
    pub fn technical(code: &'static str, message: impl Into<String>) -> Self {
        Self::new(Category::Technical, Severity::Error, code, message)
    }
    
    /// 便利构造器 - 外部错误
    pub fn external(code: &'static str, message: impl Into<String>) -> Self {
        Self::new(Category::External, Severity::Warning, code, message)
    }
    
    /// 便利构造器 - 验证错误
    pub fn validation(code: &'static str, message: impl Into<String>) -> Self {
        Self::new(Category::Validation, Severity::Error, code, message)
    }
}

impl fmt::Display for SimpleError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}:{}] {}", self.code, self.category_str(), self.message)
    }
}

impl StdError for SimpleError {
    fn source(&self) -> Option<&(dyn StdError + 'static)> {
        self.source.as_ref().map(|e| e.as_ref() as &(dyn StdError + 'static))
    }
}

impl CoreError for SimpleError {
    fn category(&self) -> Category {
        self.category
    }
    
    fn severity(&self) -> Severity {
        self.severity
    }
    
    fn error_code(&self) -> &'static str {
        self.code
    }
}

impl SimpleError {
    fn category_str(&self) -> &'static str {
        match self.category {
            Category::Business => "BIZ",
            Category::Technical => "TECH",
            Category::External => "EXT",
            Category::Validation => "VAL",
        }
    }
}

/// 结果类型别名
pub type CoreResult<T> = Result<T, SimpleError>;

/// 错误构建器 - 用于复杂场景
pub struct ErrorBuilder {
    category: Category,
    severity: Severity,
    code: &'static str,
    message: String,
    source: Option<Box<dyn StdError + Send + Sync>>,
}

impl ErrorBuilder {
    pub fn new(category: Category, code: &'static str) -> Self {
        Self {
            category,
            severity: Severity::Error, // 默认严重程度
            code,
            message: String::new(),
            source: None,
        }
    }
    
    pub fn severity(mut self, severity: Severity) -> Self {
        self.severity = severity;
        self
    }
    
    pub fn message(mut self, message: impl Into<String>) -> Self {
        self.message = message.into();
        self
    }
    
    pub fn source(mut self, source: impl StdError + Send + Sync + 'static) -> Self {
        self.source = Some(Box::new(source));
        self
    }
    
    pub fn build(self) -> SimpleError {
        SimpleError {
            category: self.category,
            severity: self.severity,
            code: self.code,
            message: self.message,
            source: self.source,
        }
    }
}

/// 便利宏 - 减少样板代码
#[macro_export]
macro_rules! simple_error {
    (business, $code:expr, $msg:expr) => {
        $crate::errors::minimal_core::SimpleError::business($code, $msg)
    };
    (technical, $code:expr, $msg:expr) => {
        $crate::errors::minimal_core::SimpleError::technical($code, $msg)
    };
    (external, $code:expr, $msg:expr) => {
        $crate::errors::minimal_core::SimpleError::external($code, $msg)
    };
    (validation, $code:expr, $msg:expr) => {
        $crate::errors::minimal_core::SimpleError::validation($code, $msg)
    };
}

/// Result 扩展 - 只提供最常用的方法
pub trait CoreResultExt<T> {
    /// 转换错误类型
    fn map_error<F>(self, f: F) -> CoreResult<T>
    where
        F: FnOnce(SimpleError) -> SimpleError;
    
    /// 添加上下文信息
    fn with_context(self, context: &str) -> CoreResult<T>;
    
    /// 记录错误并返回默认值
    fn or_default_logged(self, default: T) -> T
    where
        T: Default;
}

impl<T> CoreResultExt<T> for CoreResult<T> {
    fn map_error<F>(self, f: F) -> CoreResult<T>
    where
        F: FnOnce(SimpleError) -> SimpleError,
    {
        self.map_err(f)
    }
    
    fn with_context(self, context: &str) -> CoreResult<T> {
        self.map_err(|e| {
            SimpleError::new(
                e.category(),
                e.severity(),
                e.error_code(),
                format!("{}: {}", context, e.message),
            ).with_source(e)
        })
    }
    
    fn or_default_logged(self, default: T) -> T
    where
        T: Default,
    {
        match self {
            Ok(value) => value,
            Err(e) => {
                tracing::error!("Error occurred: {}", e);
                default
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_simple_error_creation() {
        let error = SimpleError::business("ORDER_001", "Invalid order amount");
        
        assert_eq!(error.category(), Category::Business);
        assert_eq!(error.severity(), Severity::Error);
        assert_eq!(error.error_code(), "ORDER_001");
        assert!(error.to_string().contains("Invalid order amount"));
    }
    
    #[test]
    fn test_error_builder() {
        let error = ErrorBuilder::new(Category::Technical, "DB_001")
            .severity(Severity::Critical)
            .message("Database connection failed")
            .build();
        
        assert_eq!(error.severity(), Severity::Critical);
        assert_eq!(error.error_code(), "DB_001");
    }
    
    #[test]
    fn test_result_extensions() {
        let result: CoreResult<i32> = Err(SimpleError::technical("TEST_001", "Test error"));
        
        let with_context = result.with_context("During testing");
        assert!(with_context.is_err());
        assert!(with_context.unwrap_err().to_string().contains("During testing"));
    }
    
    #[test]
    fn test_macro() {
        let error = simple_error!(business, "ORDER_002", "Order not found");
        assert_eq!(error.category(), Category::Business);
        assert_eq!(error.error_code(), "ORDER_002");
    }
}
