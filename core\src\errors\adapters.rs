//! 错误适配层
//! 
//! 提供与现有错误系统的兼容性和标准库错误的转换
//! 遵循"开放封闭原则" - 对扩展开放，对修改封闭

use super::minimal_core::{SimpleError, Category, Severity, CoreResult};
use std::collections::HashMap;

/// 错误适配器特征 - 定义转换契约
pub trait ErrorAdapter<T> {
    fn adapt(error: T) -> SimpleError;
}

/// 从标准库错误转换
impl ErrorAdapter<std::io::Error> for SimpleError {
    fn adapt(error: std::io::Error) -> SimpleError {
        let severity = match error.kind() {
            std::io::ErrorKind::NotFound => Severity::Warning,
            std::io::ErrorKind::PermissionDenied => Severity::Error,
            std::io::ErrorKind::ConnectionRefused 
            | std::io::ErrorKind::ConnectionAborted 
            | std::io::ErrorKind::ConnectionReset => Severity::Warning,
            _ => Severity::Error,
        };
        
        SimpleError::new(
            Category::Technical,
            severity,
            "IO_ERROR",
            format!("IO operation failed: {}", error),
        ).with_source(error)
    }
}

impl ErrorAdapter<serde_json::Error> for SimpleError {
    fn adapt(error: serde_json::Error) -> SimpleError {
        SimpleError::new(
            Category::Technical,
            Severity::Error,
            "JSON_ERROR",
            format!("JSON serialization failed: {}", error),
        ).with_source(error)
    }
}

impl ErrorAdapter<sqlx::Error> for SimpleError {
    fn adapt(error: sqlx::Error) -> SimpleError {
        let (severity, code) = match &error {
            sqlx::Error::RowNotFound => (Severity::Warning, "DB_NOT_FOUND"),
            sqlx::Error::Database(_) => (Severity::Error, "DB_CONSTRAINT"),
            sqlx::Error::PoolTimedOut => (Severity::Warning, "DB_TIMEOUT"),
            sqlx::Error::Io(_) => (Severity::Warning, "DB_IO"),
            _ => (Severity::Error, "DB_ERROR"),
        };
        
        SimpleError::new(
            Category::Technical,
            severity,
            code,
            format!("Database operation failed: {}", error),
        ).with_source(error)
    }
}

/// 向后兼容：从 SigmaXError 转换
impl ErrorAdapter<crate::SigmaXError> for SimpleError {
    fn adapt(error: crate::SigmaXError) -> SimpleError {
        match error {
            crate::SigmaXError::Exchange(msg) => {
                SimpleError::new(Category::External, Severity::Warning, "EXCHANGE_ERROR", msg)
            },
            crate::SigmaXError::Order(msg) => {
                SimpleError::new(Category::Business, Severity::Error, "ORDER_ERROR", msg)
            },
            crate::SigmaXError::Strategy(msg) => {
                SimpleError::new(Category::Business, Severity::Error, "STRATEGY_ERROR", msg)
            },
            crate::SigmaXError::Risk(msg) => {
                SimpleError::new(Category::Business, Severity::Critical, "RISK_ERROR", msg)
            },
            crate::SigmaXError::Data(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "DATA_ERROR", msg)
            },
            crate::SigmaXError::Network(msg) => {
                SimpleError::new(Category::Technical, Severity::Warning, "NETWORK_ERROR", msg)
            },
            crate::SigmaXError::Config(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "CONFIG_ERROR", msg)
            },
            crate::SigmaXError::Database(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "DATABASE_ERROR", msg)
            },
            crate::SigmaXError::Serialization(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "SERIALIZATION_ERROR", msg)
            },
            crate::SigmaXError::InvalidParameter(msg) => {
                SimpleError::new(Category::Validation, Severity::Error, "INVALID_PARAM", msg)
            },
            crate::SigmaXError::NotFound(msg) => {
                SimpleError::new(Category::Business, Severity::Warning, "NOT_FOUND", msg)
            },
            crate::SigmaXError::Unauthorized => {
                SimpleError::new(Category::Business, Severity::Error, "UNAUTHORIZED", "Access denied")
            },
            crate::SigmaXError::RateLimit => {
                SimpleError::new(Category::External, Severity::Warning, "RATE_LIMIT", "Rate limit exceeded")
            },
            crate::SigmaXError::Internal(msg) => {
                SimpleError::new(Category::Technical, Severity::Critical, "INTERNAL_ERROR", msg)
            },
            crate::SigmaXError::RiskManagement(msg) => {
                SimpleError::new(Category::Business, Severity::Critical, "RISK_MGMT_ERROR", msg)
            },
            crate::SigmaXError::InvalidState(msg) => {
                SimpleError::new(Category::Business, Severity::Error, "INVALID_STATE", msg)
            },
            crate::SigmaXError::InvalidOperation(msg) => {
                SimpleError::new(Category::Business, Severity::Error, "INVALID_OPERATION", msg)
            },
            crate::SigmaXError::Service(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "SERVICE_ERROR", msg)
            },
            crate::SigmaXError::ValidationError(msg) => {
                SimpleError::new(Category::Validation, Severity::Error, "VALIDATION_ERROR", msg)
            },
            crate::SigmaXError::NotImplemented(msg) => {
                SimpleError::new(Category::Technical, Severity::Error, "NOT_IMPLEMENTED", msg)
            },
            crate::SigmaXError::RateLimitExceeded(msg) => {
                SimpleError::new(Category::External, Severity::Warning, "RATE_LIMIT_EXCEEDED", msg)
            },
            crate::SigmaXError::CircuitBreakerOpen(msg) => {
                SimpleError::new(Category::External, Severity::Warning, "CIRCUIT_BREAKER_OPEN", msg)
            },
        }
    }
}

/// 向后兼容：转换为 SigmaXError
impl From<SimpleError> for crate::SigmaXError {
    fn from(error: SimpleError) -> Self {
        match (error.category(), error.error_code()) {
            (Category::Business, code) if code.starts_with("ORDER") => {
                crate::SigmaXError::Order(error.to_string())
            },
            (Category::Business, code) if code.starts_with("STRATEGY") => {
                crate::SigmaXError::Strategy(error.to_string())
            },
            (Category::Business, code) if code.starts_with("RISK") => {
                crate::SigmaXError::Risk(error.to_string())
            },
            (Category::Business, "UNAUTHORIZED") => {
                crate::SigmaXError::Unauthorized
            },
            (Category::Business, "NOT_FOUND") => {
                crate::SigmaXError::NotFound(error.to_string())
            },
            (Category::Business, "INVALID_STATE") => {
                crate::SigmaXError::InvalidState(error.to_string())
            },
            (Category::Business, "INVALID_OPERATION") => {
                crate::SigmaXError::InvalidOperation(error.to_string())
            },
            (Category::Technical, code) if code.starts_with("DB") => {
                crate::SigmaXError::Database(error.to_string())
            },
            (Category::Technical, "NETWORK_ERROR") => {
                crate::SigmaXError::Network(error.to_string())
            },
            (Category::Technical, "CONFIG_ERROR") => {
                crate::SigmaXError::Config(error.to_string())
            },
            (Category::Technical, "SERIALIZATION_ERROR") => {
                crate::SigmaXError::Serialization(error.to_string())
            },
            (Category::External, "EXCHANGE_ERROR") => {
                crate::SigmaXError::Exchange(error.to_string())
            },
            (Category::External, code) if code.contains("RATE_LIMIT") => {
                crate::SigmaXError::RateLimit
            },
            (Category::External, "CIRCUIT_BREAKER_OPEN") => {
                crate::SigmaXError::CircuitBreakerOpen(error.to_string())
            },
            (Category::Validation, _) => {
                crate::SigmaXError::ValidationError(error.to_string())
            },
            _ => {
                crate::SigmaXError::Internal(error.to_string())
            },
        }
    }
}

/// 通用转换函数
pub fn adapt_error<T>(error: T) -> SimpleError 
where 
    SimpleError: ErrorAdapter<T>
{
    SimpleError::adapt(error)
}

/// Result 转换扩展
pub trait AdapterResultExt<T, E> {
    /// 适配错误类型
    fn adapt_error(self) -> CoreResult<T>
    where
        SimpleError: ErrorAdapter<E>;
}

impl<T, E> AdapterResultExt<T, E> for Result<T, E> {
    fn adapt_error(self) -> CoreResult<T>
    where
        SimpleError: ErrorAdapter<E>,
    {
        self.map_err(SimpleError::adapt)
    }
}

/// 错误映射配置 - 用于自定义错误映射
#[derive(Debug, Clone)]
pub struct ErrorMapping {
    mappings: HashMap<String, (&'static str, Category, Severity)>,
}

impl ErrorMapping {
    pub fn new() -> Self {
        Self {
            mappings: HashMap::new(),
        }
    }
    
    pub fn add_mapping(
        mut self,
        pattern: impl Into<String>,
        code: &'static str,
        category: Category,
        severity: Severity,
    ) -> Self {
        self.mappings.insert(pattern.into(), (code, category, severity));
        self
    }
    
    pub fn map_error(&self, error_message: &str) -> Option<(&'static str, Category, Severity)> {
        for (pattern, mapping) in &self.mappings {
            if error_message.contains(pattern) {
                return Some(*mapping);
            }
        }
        None
    }
}

impl Default for ErrorMapping {
    fn default() -> Self {
        Self::new()
            .add_mapping("connection refused", "CONN_REFUSED", Category::External, Severity::Warning)
            .add_mapping("timeout", "TIMEOUT", Category::Technical, Severity::Warning)
            .add_mapping("permission denied", "PERMISSION_DENIED", Category::Business, Severity::Error)
            .add_mapping("not found", "NOT_FOUND", Category::Business, Severity::Warning)
            .add_mapping("invalid", "INVALID_INPUT", Category::Validation, Severity::Error)
    }
}

/// 智能错误适配器 - 使用配置进行映射
pub struct SmartAdapter {
    mapping: ErrorMapping,
}

impl SmartAdapter {
    pub fn new(mapping: ErrorMapping) -> Self {
        Self { mapping }
    }
    
    pub fn adapt_string_error(&self, message: &str) -> SimpleError {
        if let Some((code, category, severity)) = self.mapping.map_error(message) {
            SimpleError::new(category, severity, code, message)
        } else {
            SimpleError::new(Category::Technical, Severity::Error, "UNKNOWN_ERROR", message)
        }
    }
}

impl Default for SmartAdapter {
    fn default() -> Self {
        Self::new(ErrorMapping::default())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::simple_error;
    
    #[test]
    fn test_io_error_adapter() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "File not found");
        let simple_error = SimpleError::adapt(io_error);
        
        assert_eq!(simple_error.category(), Category::Technical);
        assert_eq!(simple_error.error_code(), "IO_ERROR");
        assert_eq!(simple_error.severity(), Severity::Warning);
    }
    
    #[test]
    fn test_sigmax_error_conversion() {
        let sigmax_error = crate::SigmaXError::Order("Invalid order".to_string());
        let simple_error = SimpleError::adapt(sigmax_error);
        
        assert_eq!(simple_error.category(), Category::Business);
        assert_eq!(simple_error.error_code(), "ORDER_ERROR");
        
        // 测试反向转换
        let back_to_sigmax: crate::SigmaXError = simple_error.into();
        match back_to_sigmax {
            crate::SigmaXError::Order(_) => {},
            _ => panic!("Expected Order error"),
        }
    }
    
    #[test]
    fn test_result_adapter() {
        let result: Result<i32, std::io::Error> = Err(
            std::io::Error::new(std::io::ErrorKind::PermissionDenied, "Access denied")
        );
        
        let adapted = result.adapt_error();
        assert!(adapted.is_err());
        
        let error = adapted.unwrap_err();
        assert_eq!(error.category(), Category::Technical);
        assert_eq!(error.severity(), Severity::Error);
    }
    
    #[test]
    fn test_error_mapping() {
        let mapping = ErrorMapping::new()
            .add_mapping("database", "DB_ERROR", Category::Technical, Severity::Error);
        
        let adapter = SmartAdapter::new(mapping);
        let error = adapter.adapt_string_error("database connection failed");
        
        assert_eq!(error.error_code(), "DB_ERROR");
        assert_eq!(error.category(), Category::Technical);
    }
}
