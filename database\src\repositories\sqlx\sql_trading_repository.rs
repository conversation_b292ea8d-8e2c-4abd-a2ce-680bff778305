//! SQLx 交易配置仓储实现
//!
//! 基于 SQLx 的交易配置数据访问实现，使用 JSONB 存储完整配置

use std::sync::Arc;
use async_trait::async_trait;
use sqlx::Row;
use uuid::Uuid;
use serde_json;
use tracing::{info, warn, debug};

use sigmax_core::{SigmaXResult, SigmaXError, TradingConfig};
use crate::DatabaseManager;
use crate::repositories::traits::trading_repository::{TradingRepository, TradingConfigRecord, TradingConfigStatistics};

/// SQLx 交易配置仓储实现
///
/// 使用 PostgreSQL JSONB 存储完整的交易配置，提供高效的查询和更新操作
pub struct SqlTradingRepository {
    db: Arc<DatabaseManager>,
}

impl SqlTradingRepository {
    /// 创建新的交易配置仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 获取数据库连接池
    fn get_pool(&self) -> &sqlx::PgPool {
        self.db.pool()
    }

    /// 将数据库行转换为 TradingConfigRecord
    fn row_to_config_record(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<TradingConfigRecord> {
        let trading_parameters_json: serde_json::Value = row.try_get("trading_parameters")
            .map_err(|e| SigmaXError::database_error(format!("Failed to get trading_parameters: {}", e)))?;

        let config: TradingConfig = serde_json::from_value(trading_parameters_json)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize trading config: {}", e)))?;

        Ok(TradingConfigRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get id: {}", e)))?,
            name: row.try_get("name")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get name: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get description: {}", e)))?,
            enabled: row.try_get("enabled")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get enabled: {}", e)))?,
            config,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get created_at: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get updated_at: {}", e)))?,
            created_by: row.try_get("created_by")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get created_by: {}", e)))?,
        })
    }

    /// 将 TradingConfig 转换为 JSONB
    fn config_to_jsonb(&self, config: &TradingConfig) -> SigmaXResult<serde_json::Value> {
        serde_json::to_value(config)
            .map_err(|e| SigmaXError::Serialization(format!("Failed to serialize trading config: {}", e)))
    }

    /// 验证配置参数
    fn validate_config(&self, config: &TradingConfig) -> SigmaXResult<()> {
        if config.max_orders_per_strategy == 0 {
            return Err(SigmaXError::ValidationError("max_orders_per_strategy must be greater than 0".to_string()));
        }

        if config.max_orders_per_strategy > 1000 {
            return Err(SigmaXError::ValidationError("max_orders_per_strategy cannot exceed 1000".to_string()));
        }

        if config.max_position_size <= rust_decimal::Decimal::ZERO {
            return Err(SigmaXError::ValidationError("max_position_size must be greater than 0".to_string()));
        }

        if config.default_order_timeout == 0 {
            return Err(SigmaXError::ValidationError("default_order_timeout must be greater than 0".to_string()));
        }

        if config.min_order_interval == 0 {
            return Err(SigmaXError::ValidationError("min_order_interval must be greater than 0".to_string()));
        }

        if config.max_slippage_percent < 0.0 || config.max_slippage_percent > 100.0 {
            return Err(SigmaXError::ValidationError("max_slippage_percent must be between 0 and 100".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl TradingRepository for SqlTradingRepository {
    // ============================================================================
    // 交易配置管理实现 - 与其他配置服务保持一致
    // ============================================================================

    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig> {
        debug!("Getting current enabled trading config");

        let query = r#"
            SELECT trading_parameters
            FROM trading_config
            WHERE enabled = true
            ORDER BY updated_at DESC
            LIMIT 1
        "#;

        let row = sqlx::query(query)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to query trading config: {}", e)))?;

        match row {
            Some(row) => {
                let trading_parameters_json: serde_json::Value = row.try_get("trading_parameters")
                    .map_err(|e| SigmaXError::database_error(format!("Failed to get trading_parameters: {}", e)))?;

                let config: TradingConfig = serde_json::from_value(trading_parameters_json)
                    .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize trading config: {}", e)))?;

                debug!("Successfully retrieved trading config");
                Ok(config)
            }
            None => {
                warn!("No enabled trading config found, returning default config");
                Ok(TradingConfig::default())
            }
        }
    }

    async fn save_trading_config(&self, config: &TradingConfig) -> SigmaXResult<()> {
        debug!("Saving trading config");

        // 验证配置
        self.validate_config(config)?;

        let pool = self.get_pool();
        let mut tx = pool.begin()
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to begin transaction: {}", e)))?;

        // 禁用所有现有配置
        sqlx::query("UPDATE trading_config SET enabled = false")
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to disable existing configs: {}", e)))?;

        // 插入新配置
        let config_json = self.config_to_jsonb(config)?;
        let config_id = Uuid::new_v4();

        sqlx::query(
            r#"
            INSERT INTO trading_config (id, name, description, enabled, trading_parameters, created_at, updated_at, created_by)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW(), $6)
            "#
        )
        .bind(&config_id)
        .bind("default")
        .bind("Default trading configuration")
        .bind(true)
        .bind(&config_json)
        .bind("system")
        .execute(&mut *tx)
        .await
        .map_err(|e| SigmaXError::database_error(format!("Failed to insert trading config: {}", e)))?;

        tx.commit()
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to commit transaction: {}", e)))?;

        info!("Trading config saved successfully with id: {}", config_id);
        Ok(())
    }

    async fn reset_trading_config(&self) -> SigmaXResult<()> {
        debug!("Resetting trading config to default");
        let default_config = TradingConfig::default();
        self.save_trading_config(&default_config).await
    }

    // ============================================================================
    // 交易配置记录管理实现 - 完整的CRUD操作
    // ============================================================================

    async fn get_config(&self) -> SigmaXResult<TradingConfig> {
        debug!("Getting current enabled trading config");

        let query = r#"
            SELECT trading_parameters 
            FROM trading_config 
            WHERE enabled = true 
            ORDER BY updated_at DESC 
            LIMIT 1
        "#;

        let row = sqlx::query(query)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to query trading config: {}", e)))?;

        match row {
            Some(row) => {
                let trading_parameters_json: serde_json::Value = row.try_get("trading_parameters")
                    .map_err(|e| SigmaXError::database_error(format!("Failed to get trading_parameters: {}", e)))?;

                let config: TradingConfig = serde_json::from_value(trading_parameters_json)
                    .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize trading config: {}", e)))?;

                debug!("Successfully retrieved trading config");
                Ok(config)
            }
            None => {
                warn!("No enabled trading config found, returning default config");
                Ok(TradingConfig::default())
            }
        }
    }

    async fn get_config_by_id(&self, id: Uuid) -> SigmaXResult<Option<TradingConfig>> {
        debug!("Getting trading config by id: {}", id);

        let query = r#"
            SELECT trading_parameters 
            FROM trading_config 
            WHERE id = $1
        "#;

        let row = sqlx::query(query)
            .bind(id)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to query trading config by id: {}", e)))?;

        match row {
            Some(row) => {
                let trading_parameters_json: serde_json::Value = row.try_get("trading_parameters")
                    .map_err(|e| SigmaXError::database_error(format!("Failed to get trading_parameters: {}", e)))?;

                let config: TradingConfig = serde_json::from_value(trading_parameters_json)
                    .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize trading config: {}", e)))?;

                Ok(Some(config))
            }
            None => Ok(None)
        }
    }

    async fn get_config_by_name(&self, name: &str) -> SigmaXResult<Option<TradingConfig>> {
        debug!("Getting trading config by name: {}", name);

        let query = r#"
            SELECT trading_parameters 
            FROM trading_config 
            WHERE name = $1
        "#;

        let row = sqlx::query(query)
            .bind(name)
            .fetch_optional(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to query trading config by name: {}", e)))?;

        match row {
            Some(row) => {
                let trading_parameters_json: serde_json::Value = row.try_get("trading_parameters")
                    .map_err(|e| SigmaXError::database_error(format!("Failed to get trading_parameters: {}", e)))?;

                let config: TradingConfig = serde_json::from_value(trading_parameters_json)
                    .map_err(|e| SigmaXError::Serialization(format!("Failed to deserialize trading config: {}", e)))?;

                Ok(Some(config))
            }
            None => Ok(None)
        }
    }

    async fn save_config(&self, config: &TradingConfig) -> SigmaXResult<Uuid> {
        debug!("Saving new trading config");

        // 验证配置
        self.validate_config(config)?;

        // 转换为 JSONB
        let config_json = self.config_to_jsonb(config)?;

        let query = r#"
            INSERT INTO trading_config (
                name, description, enabled, trading_parameters, created_by
            ) VALUES (
                $1, $2, $3, $4, $5
            ) RETURNING id
        "#;

        let row = sqlx::query(query)
            .bind("default")
            .bind("系统默认交易配置")
            .bind(true)
            .bind(config_json)
            .bind("system")
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to save trading config: {}", e)))?;

        let id: Uuid = row.try_get("id")
            .map_err(|e| SigmaXError::database_error(format!("Failed to get inserted id: {}", e)))?;

        info!("Successfully saved trading config with id: {}", id);
        Ok(id)
    }

    async fn update_config(&self, id: Uuid, config: &TradingConfig) -> SigmaXResult<()> {
        debug!("Updating trading config with id: {}", id);

        // 验证配置
        self.validate_config(config)?;

        // 转换为 JSONB
        let config_json = self.config_to_jsonb(config)?;

        let query = r#"
            UPDATE trading_config 
            SET trading_parameters = $1, updated_at = NOW()
            WHERE id = $2
        "#;

        let result = sqlx::query(query)
            .bind(config_json)
            .bind(id)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to update trading config: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Trading config with id {} not found", id)));
        }

        info!("Successfully updated trading config with id: {}", id);
        Ok(())
    }

    async fn delete_config(&self, id: Uuid) -> SigmaXResult<()> {
        debug!("Deleting trading config with id: {}", id);

        let query = "DELETE FROM trading_config WHERE id = $1";

        let result = sqlx::query(query)
            .bind(id)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to delete trading config: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Trading config with id {} not found", id)));
        }

        info!("Successfully deleted trading config with id: {}", id);
        Ok(())
    }

    async fn enable_config(&self, id: Uuid) -> SigmaXResult<()> {
        debug!("Enabling trading config with id: {}", id);

        let mut tx = self.get_pool().begin().await
            .map_err(|e| SigmaXError::database_error(format!("Failed to begin transaction: {}", e)))?;

        // 禁用所有配置
        sqlx::query("UPDATE trading_config SET enabled = false")
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to disable all configs: {}", e)))?;

        // 启用指定配置
        let result = sqlx::query("UPDATE trading_config SET enabled = true WHERE id = $1")
            .bind(id)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to enable config: {}", e)))?;

        if result.rows_affected() == 0 {
            tx.rollback().await
                .map_err(|e| SigmaXError::database_error(format!("Failed to rollback transaction: {}", e)))?;
            return Err(SigmaXError::NotFound(format!("Trading config with id {} not found", id)));
        }

        tx.commit().await
            .map_err(|e| SigmaXError::database_error(format!("Failed to commit transaction: {}", e)))?;

        info!("Successfully enabled trading config with id: {}", id);
        Ok(())
    }

    async fn disable_config(&self, id: Uuid) -> SigmaXResult<()> {
        debug!("Disabling trading config with id: {}", id);

        let query = "UPDATE trading_config SET enabled = false WHERE id = $1";

        let result = sqlx::query(query)
            .bind(id)
            .execute(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to disable trading config: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::NotFound(format!("Trading config with id {} not found", id)));
        }

        info!("Successfully disabled trading config with id: {}", id);
        Ok(())
    }

    async fn reset_to_default(&self) -> SigmaXResult<Uuid> {
        debug!("Resetting to default trading config");

        let default_config = TradingConfig::default();
        let config_json = self.config_to_jsonb(&default_config)?;

        let mut tx = self.get_pool().begin().await
            .map_err(|e| SigmaXError::database_error(format!("Failed to begin transaction: {}", e)))?;

        // 禁用所有现有配置
        sqlx::query("UPDATE trading_config SET enabled = false")
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to disable all configs: {}", e)))?;

        // 插入新的默认配置
        let query = r#"
            INSERT INTO trading_config (
                name, description, enabled, trading_parameters, created_by
            ) VALUES (
                'default', '系统默认交易配置', true, $1, 'system'
            ) RETURNING id
        "#;

        let row = sqlx::query(query)
            .bind(config_json)
            .fetch_one(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to insert default config: {}", e)))?;

        let id: Uuid = row.try_get("id")
            .map_err(|e| SigmaXError::database_error(format!("Failed to get inserted id: {}", e)))?;

        tx.commit().await
            .map_err(|e| SigmaXError::database_error(format!("Failed to commit transaction: {}", e)))?;

        info!("Successfully reset to default trading config with id: {}", id);
        Ok(id)
    }

    async fn list_configs(&self, enabled_only: bool) -> SigmaXResult<Vec<TradingConfigRecord>> {
        debug!("Listing trading configs, enabled_only: {}", enabled_only);

        let query = if enabled_only {
            r#"
                SELECT id, name, description, enabled, trading_parameters, 
                       created_at, updated_at, created_by
                FROM trading_config 
                WHERE enabled = true
                ORDER BY updated_at DESC
            "#
        } else {
            r#"
                SELECT id, name, description, enabled, trading_parameters, 
                       created_at, updated_at, created_by
                FROM trading_config 
                ORDER BY updated_at DESC
            "#
        };

        let rows = sqlx::query(query)
            .fetch_all(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to list trading configs: {}", e)))?;

        let mut configs = Vec::new();
        for row in rows {
            configs.push(self.row_to_config_record(&row)?);
        }

        debug!("Successfully listed {} trading configs", configs.len());
        Ok(configs)
    }

    async fn config_exists(&self, name: &str) -> SigmaXResult<bool> {
        debug!("Checking if trading config exists: {}", name);

        let query = "SELECT COUNT(*) as count FROM trading_config WHERE name = $1";

        let row = sqlx::query(query)
            .bind(name)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to check config existence: {}", e)))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| SigmaXError::database_error(format!("Failed to get count: {}", e)))?;

        Ok(count > 0)
    }

    async fn get_statistics(&self) -> SigmaXResult<TradingConfigStatistics> {
        debug!("Getting trading config statistics");

        let query = r#"
            SELECT 
                COUNT(*) as total_configs,
                COUNT(*) FILTER (WHERE enabled = true) as enabled_configs,
                MAX(updated_at) as last_updated,
                (SELECT id FROM trading_config WHERE enabled = true LIMIT 1) as active_config_id
            FROM trading_config
        "#;

        let row = sqlx::query(query)
            .fetch_one(self.get_pool())
            .await
            .map_err(|e| SigmaXError::database_error(format!("Failed to get statistics: {}", e)))?;

        let stats = TradingConfigStatistics {
            total_configs: row.try_get::<i64, _>("total_configs")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get total_configs: {}", e)))? as u64,
            enabled_configs: row.try_get::<i64, _>("enabled_configs")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get enabled_configs: {}", e)))? as u64,
            last_updated: row.try_get("last_updated")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get last_updated: {}", e)))?,
            active_config_id: row.try_get("active_config_id")
                .map_err(|e| SigmaXError::database_error(format!("Failed to get active_config_id: {}", e)))?,
        };

        debug!("Successfully retrieved trading config statistics");
        Ok(stats)
    }
}
