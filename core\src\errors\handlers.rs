//! 错误处理器
//! 
//! 遵循可测试性设计原则：支持依赖注入，便于Mock测试
//! 定义清晰的错误处理接口契约

use super::domain::{DomainError, ErrorCategory, ErrorSeverity};
use std::collections::HashMap;
use std::sync::Arc;
use async_trait::async_trait;

/// 错误处理器接口 - 面向接口设计
#[async_trait]
pub trait ErrorHandler: Send + Sync {
    /// 处理错误
    async fn handle_error(&self, error: &dyn DomainError) -> ErrorHandlingResult;
    
    /// 是否可以处理此类错误
    fn can_handle(&self, error: &dyn DomainError) -> bool;
    
    /// 处理器优先级（数字越小优先级越高）
    fn priority(&self) -> u8;
}

/// 错误处理结果
#[derive(Debug, Clone)]
pub struct ErrorHandlingResult {
    /// 是否已处理
    pub handled: bool,
    /// 是否应该继续传播错误
    pub should_propagate: bool,
    /// 处理后的动作
    pub action: ErrorAction,
    /// 附加信息
    pub metadata: HashMap<String, String>,
}

/// 错误处理动作
#[derive(Debug, Clone)]
pub enum ErrorAction {
    /// 忽略错误
    Ignore,
    /// 记录错误
    Log { level: LogLevel },
    /// 重试操作
    Retry { 
        max_attempts: u8,
        delay_ms: u64,
        backoff_factor: f64,
    },
    /// 降级处理
    Fallback { fallback_value: String },
    /// 熔断
    CircuitBreak { duration_ms: u64 },
    /// 告警
    Alert { 
        severity: AlertSeverity,
        message: String,
    },
}

#[derive(Debug, Clone)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

#[derive(Debug, Clone)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 默认错误处理器 - 基础实现
pub struct DefaultErrorHandler {
    config: ErrorHandlerConfig,
}

#[derive(Debug, Clone)]
pub struct ErrorHandlerConfig {
    /// 是否启用日志记录
    pub enable_logging: bool,
    /// 是否启用指标收集
    pub enable_metrics: bool,
    /// 是否启用告警
    pub enable_alerting: bool,
    /// 日志级别映射
    pub log_level_mapping: HashMap<ErrorSeverity, LogLevel>,
}

impl Default for ErrorHandlerConfig {
    fn default() -> Self {
        let mut log_level_mapping = HashMap::new();
        log_level_mapping.insert(ErrorSeverity::Info, LogLevel::Info);
        log_level_mapping.insert(ErrorSeverity::Warning, LogLevel::Warn);
        log_level_mapping.insert(ErrorSeverity::Error, LogLevel::Error);
        log_level_mapping.insert(ErrorSeverity::Critical, LogLevel::Error);
        
        Self {
            enable_logging: true,
            enable_metrics: true,
            enable_alerting: true,
            log_level_mapping,
        }
    }
}

impl DefaultErrorHandler {
    pub fn new(config: ErrorHandlerConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl ErrorHandler for DefaultErrorHandler {
    async fn handle_error(&self, error: &dyn DomainError) -> ErrorHandlingResult {
        let mut metadata = HashMap::new();
        let mut actions = Vec::new();
        
        // 记录日志
        if self.config.enable_logging {
            let log_level = self.config.log_level_mapping
                .get(&error.severity())
                .cloned()
                .unwrap_or(LogLevel::Error);
            
            actions.push(ErrorAction::Log { level: log_level });
        }
        
        // 根据错误类别和严重程度决定处理策略
        let action = match (error.category(), error.severity()) {
            (ErrorCategory::External, ErrorSeverity::Warning) if error.is_retryable() => {
                ErrorAction::Retry {
                    max_attempts: 3,
                    delay_ms: 1000,
                    backoff_factor: 2.0,
                }
            },
            (ErrorCategory::Infrastructure, ErrorSeverity::Critical) => {
                ErrorAction::Alert {
                    severity: AlertSeverity::Critical,
                    message: format!("Critical infrastructure error: {}", error),
                }
            },
            (ErrorCategory::Business, ErrorSeverity::Critical) => {
                ErrorAction::Alert {
                    severity: AlertSeverity::High,
                    message: format!("Critical business error: {}", error),
                }
            },
            _ => ErrorAction::Log { level: LogLevel::Error },
        };
        
        metadata.insert("handler".to_string(), "default".to_string());
        metadata.insert("error_code".to_string(), error.error_code().to_string());
        metadata.insert("category".to_string(), format!("{:?}", error.category()));
        metadata.insert("severity".to_string(), format!("{:?}", error.severity()));
        
        ErrorHandlingResult {
            handled: true,
            should_propagate: matches!(error.severity(), ErrorSeverity::Critical),
            action,
            metadata,
        }
    }
    
    fn can_handle(&self, _error: &dyn DomainError) -> bool {
        true // 默认处理器可以处理所有错误
    }
    
    fn priority(&self) -> u8 {
        255 // 最低优先级，作为兜底处理器
    }
}

/// 重试错误处理器 - 专门处理可重试错误
pub struct RetryErrorHandler {
    max_attempts: u8,
    base_delay_ms: u64,
    backoff_factor: f64,
    max_delay_ms: u64,
}

impl RetryErrorHandler {
    pub fn new(max_attempts: u8, base_delay_ms: u64, backoff_factor: f64, max_delay_ms: u64) -> Self {
        Self {
            max_attempts,
            base_delay_ms,
            backoff_factor,
            max_delay_ms,
        }
    }
}

#[async_trait]
impl ErrorHandler for RetryErrorHandler {
    async fn handle_error(&self, error: &dyn DomainError) -> ErrorHandlingResult {
        let mut metadata = HashMap::new();
        metadata.insert("handler".to_string(), "retry".to_string());
        
        if error.is_retryable() {
            ErrorHandlingResult {
                handled: true,
                should_propagate: false,
                action: ErrorAction::Retry {
                    max_attempts: self.max_attempts,
                    delay_ms: self.base_delay_ms,
                    backoff_factor: self.backoff_factor,
                },
                metadata,
            }
        } else {
            ErrorHandlingResult {
                handled: false,
                should_propagate: true,
                action: ErrorAction::Log { level: LogLevel::Error },
                metadata,
            }
        }
    }
    
    fn can_handle(&self, error: &dyn DomainError) -> bool {
        error.is_retryable()
    }
    
    fn priority(&self) -> u8 {
        10 // 高优先级
    }
}

/// 错误处理器链 - 责任链模式
pub struct ErrorHandlerChain {
    handlers: Vec<Arc<dyn ErrorHandler>>,
}

impl ErrorHandlerChain {
    pub fn new() -> Self {
        Self {
            handlers: Vec::new(),
        }
    }
    
    /// 添加错误处理器
    pub fn add_handler(mut self, handler: Arc<dyn ErrorHandler>) -> Self {
        self.handlers.push(handler);
        // 按优先级排序
        self.handlers.sort_by_key(|h| h.priority());
        self
    }
    
    /// 处理错误
    pub async fn handle_error(&self, error: &dyn DomainError) -> Vec<ErrorHandlingResult> {
        let mut results = Vec::new();
        
        for handler in &self.handlers {
            if handler.can_handle(error) {
                let result = handler.handle_error(error).await;
                let should_continue = !result.handled || result.should_propagate;
                results.push(result);
                
                if !should_continue {
                    break;
                }
            }
        }
        
        results
    }
}

impl Default for ErrorHandlerChain {
    fn default() -> Self {
        Self::new()
            .add_handler(Arc::new(RetryErrorHandler::new(3, 1000, 2.0, 30000)))
            .add_handler(Arc::new(DefaultErrorHandler::new(ErrorHandlerConfig::default())))
    }
}

/// 错误处理器工厂 - 便于测试和依赖注入
pub trait ErrorHandlerFactory: Send + Sync {
    fn create_handler(&self, handler_type: &str) -> Option<Arc<dyn ErrorHandler>>;
}

/// 默认错误处理器工厂
pub struct DefaultErrorHandlerFactory;

impl ErrorHandlerFactory for DefaultErrorHandlerFactory {
    fn create_handler(&self, handler_type: &str) -> Option<Arc<dyn ErrorHandler>> {
        match handler_type {
            "default" => Some(Arc::new(DefaultErrorHandler::new(ErrorHandlerConfig::default()))),
            "retry" => Some(Arc::new(RetryErrorHandler::new(3, 1000, 2.0, 30000))),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::errors::domain::TradingError;
    
    /// Mock错误处理器 - 用于测试
    pub struct MockErrorHandler {
        pub should_handle: bool,
        pub priority: u8,
        pub result: ErrorHandlingResult,
    }
    
    impl MockErrorHandler {
        pub fn new(should_handle: bool, priority: u8) -> Self {
            Self {
                should_handle,
                priority,
                result: ErrorHandlingResult {
                    handled: true,
                    should_propagate: false,
                    action: ErrorAction::Log { level: LogLevel::Info },
                    metadata: HashMap::new(),
                },
            }
        }
        
        pub fn with_result(mut self, result: ErrorHandlingResult) -> Self {
            self.result = result;
            self
        }
    }
    
    #[async_trait]
    impl ErrorHandler for MockErrorHandler {
        async fn handle_error(&self, _error: &dyn DomainError) -> ErrorHandlingResult {
            self.result.clone()
        }
        
        fn can_handle(&self, _error: &dyn DomainError) -> bool {
            self.should_handle
        }
        
        fn priority(&self) -> u8 {
            self.priority
        }
    }
    
    #[tokio::test]
    async fn test_default_error_handler() {
        let handler = DefaultErrorHandler::new(ErrorHandlerConfig::default());
        let error = TradingError::InvalidOrder {
            message: "Test error".to_string(),
            order_id: None,
        };
        
        let result = handler.handle_error(&error).await;
        assert!(result.handled);
        assert!(matches!(result.action, ErrorAction::Log { .. }));
    }
    
    #[tokio::test]
    async fn test_retry_error_handler() {
        let handler = RetryErrorHandler::new(3, 1000, 2.0, 30000);
        let error = TradingError::InvalidOrder {
            message: "Test error".to_string(),
            order_id: None,
        };
        
        let result = handler.handle_error(&error).await;
        // TradingError 默认不可重试
        assert!(!result.handled);
    }
    
    #[tokio::test]
    async fn test_error_handler_chain() {
        let chain = ErrorHandlerChain::new()
            .add_handler(Arc::new(MockErrorHandler::new(true, 1)))
            .add_handler(Arc::new(MockErrorHandler::new(true, 2)));
        
        let error = TradingError::InvalidOrder {
            message: "Test error".to_string(),
            order_id: None,
        };
        
        let results = chain.handle_error(&error).await;
        assert_eq!(results.len(), 1); // 第一个处理器处理后就停止了
    }
}
