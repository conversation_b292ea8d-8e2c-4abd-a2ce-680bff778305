//! 增强信号生成演示
//! 
//! 展示完整的技术指标计算和智能信号生成功能

use sigmax_core::{Candle, Price, TradingPair, SigmaXResult};
use sigmax_strategies::{
    signal_generator::{EnhancedSignalGenerator, SignalGeneratorConfig},
    indicators::TechnicalIndicators,
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 SigmaX增强信号生成系统演示");
    println!("{}", "=".repeat(60));

    // 1. 技术指标演示
    demo_technical_indicators().await?;

    // 2. 信号生成演示
    demo_signal_generation().await?;

    // 3. 多策略信号演示
    demo_multi_strategy_signals().await?;

    // 4. 信号过滤演示
    demo_signal_filtering().await?;

    println!("\n🎉 增强信号生成系统演示完成！");
    println!("✅ 所有功能模块运行正常");

    Ok(())
}

/// 演示技术指标计算
async fn demo_technical_indicators() -> SigmaXResult<()> {
    println!("\n📊 1. 技术指标计算演示");
    println!("{}", "-".repeat(40));

    let mut indicators = TechnicalIndicators::new();

    // 模拟价格数据
    let prices = vec![
        dec!(50000), dec!(50100), dec!(49900), dec!(50200), dec!(50300),
        dec!(50150), dec!(49800), dec!(50400), dec!(50500), dec!(50250),
        dec!(49700), dec!(50600), dec!(50700), dec!(50350), dec!(49600),
        dec!(50800), dec!(50900), dec!(50450), dec!(49500), dec!(51000),
        dec!(51100), dec!(50550), dec!(49400), dec!(51200), dec!(51300),
    ];

    println!("📈 处理 {} 个价格数据点", prices.len());

    for (i, price) in prices.iter().enumerate() {
        let candle = Candle {
            timestamp: Utc::now(),
            open: *price,
            high: *price * dec!(1.002),
            low: *price * dec!(0.998),
            close: *price,
            volume: dec!(100),
        };

        let results = indicators.update(&candle);

        // 显示最后几个结果
        if i >= prices.len() - 3 {
            println!("\n📊 第 {} 个数据点结果:", i + 1);
            
            if let Some(rsi) = results.rsi {
                println!("   RSI: {:.2}", rsi);
            }
            
            if let Some(macd) = &results.macd {
                println!("   MACD: {:.4}, Signal: {:.4}, Histogram: {:.4}", 
                    macd.macd_line, macd.signal_line, macd.histogram);
            }
            
            if let Some(bollinger) = &results.bollinger {
                println!("   布林带: 上轨 {}, 中轨 {}, 下轨 {}", 
                    bollinger.upper_band, bollinger.middle_band, bollinger.lower_band);
            }
            
            if let Some(kdj) = &results.kdj {
                println!("   KDJ: K {:.2}, D {:.2}, J {:.2}", kdj.k, kdj.d, kdj.j);
            }
        }
    }

    println!("\n✅ 技术指标计算完成");
    Ok(())
}

/// 演示信号生成
async fn demo_signal_generation() -> SigmaXResult<()> {
    println!("\n🎯 2. 智能信号生成演示");
    println!("{}", "-".repeat(40));

    let config = SignalGeneratorConfig {
        rsi_overbought: 70.0,
        rsi_oversold: 30.0,
        macd_sensitivity: 0.001,
        min_signal_strength: 0.5,
        ..Default::default()
    };

    let mut signal_generator = EnhancedSignalGenerator::new(config);
    let strategy_id = Uuid::new_v4();
    let trading_pair = TradingPair::new("BTC".to_string(), "USDT".to_string());

    // 模拟不同市场条件的价格数据
    let scenarios = vec![
        ("超卖反弹", vec![dec!(50000), dec!(49000), dec!(48000), dec!(47000), dec!(48500)]),
        ("超买回调", vec![dec!(50000), dec!(51000), dec!(52000), dec!(53000), dec!(52500)]),
        ("震荡整理", vec![dec!(50000), dec!(50100), dec!(49900), dec!(50050), dec!(49950)]),
        ("突破上涨", vec![dec!(50000), dec!(50200), dec!(50500), dec!(50800), dec!(51200)]),
    ];

    for (scenario_name, prices) in scenarios {
        println!("\n📈 情景: {}", scenario_name);
        
        let mut total_signals = 0;
        let mut buy_signals = 0;
        let mut sell_signals = 0;

        for price in prices {
            let candle = Candle {
                timestamp: Utc::now(),
                open: price,
                high: price * dec!(1.005),
                low: price * dec!(0.995),
                close: price,
                volume: dec!(1000),
            };

            let signals = signal_generator.generate_signals(
                &candle,
                strategy_id,
                trading_pair.clone(),
            )?;

            for signal in signals {
                total_signals += 1;
                match signal.signal_type {
                    sigmax_strategies::signals::SignalType::Buy => {
                        buy_signals += 1;
                        println!("   🟢 买入信号: 强度 {:.2}, 价格 {}, 原因: {}", 
                            signal.strength.value(), signal.price, signal.reason);
                    }
                    sigmax_strategies::signals::SignalType::Sell => {
                        sell_signals += 1;
                        println!("   🔴 卖出信号: 强度 {:.2}, 价格 {}, 原因: {}", 
                            signal.strength.value(), signal.price, signal.reason);
                    }
                    _ => {}
                }
            }
        }

        println!("   📊 信号统计: 总计 {}, 买入 {}, 卖出 {}", total_signals, buy_signals, sell_signals);
    }

    println!("\n✅ 信号生成演示完成");
    Ok(())
}

/// 演示多策略信号
async fn demo_multi_strategy_signals() -> SigmaXResult<()> {
    println!("\n🔄 3. 多策略信号协调演示");
    println!("{}", "-".repeat(40));

    // 创建不同配置的信号生成器
    let conservative_config = SignalGeneratorConfig {
        rsi_overbought: 80.0,
        rsi_oversold: 20.0,
        min_signal_strength: 0.8,
        ..Default::default()
    };

    let aggressive_config = SignalGeneratorConfig {
        rsi_overbought: 60.0,
        rsi_oversold: 40.0,
        min_signal_strength: 0.3,
        ..Default::default()
    };

    let mut conservative_generator = EnhancedSignalGenerator::new(conservative_config);
    let mut aggressive_generator = EnhancedSignalGenerator::new(aggressive_config);

    let strategy_id = Uuid::new_v4();
    let trading_pair = TradingPair::new("ETH".to_string(), "USDT".to_string());

    // 测试价格
    let test_price = dec!(3000);
    let candle = Candle {
        timestamp: Utc::now(),
        open: test_price,
        high: test_price * dec!(1.01),
        low: test_price * dec!(0.99),
        close: test_price,
        volume: dec!(500),
    };

    let conservative_signals = conservative_generator.generate_signals(
        &candle, strategy_id, trading_pair.clone()
    )?;

    let aggressive_signals = aggressive_generator.generate_signals(
        &candle, strategy_id, trading_pair.clone()
    )?;

    println!("📊 保守策略信号数量: {}", conservative_signals.len());
    println!("📊 激进策略信号数量: {}", aggressive_signals.len());

    if !conservative_signals.is_empty() {
        println!("   保守策略信号:");
        for signal in &conservative_signals {
            println!("     - {}: 强度 {:.2}", signal.reason, signal.strength.value());
        }
    }

    if !aggressive_signals.is_empty() {
        println!("   激进策略信号:");
        for signal in &aggressive_signals {
            println!("     - {}: 强度 {:.2}", signal.reason, signal.strength.value());
        }
    }

    println!("\n✅ 多策略信号协调演示完成");
    Ok(())
}

/// 演示信号过滤
async fn demo_signal_filtering() -> SigmaXResult<()> {
    println!("\n🔍 4. 信号过滤和验证演示");
    println!("{}", "-".repeat(40));

    let config = SignalGeneratorConfig {
        min_signal_strength: 0.7, // 较高的最小强度要求
        ..Default::default()
    };

    let mut signal_generator = EnhancedSignalGenerator::new(config);
    let strategy_id = Uuid::new_v4();
    let trading_pair = TradingPair::new("ADA".to_string(), "USDT".to_string());

    println!("🔧 配置信号过滤器:");
    println!("   - 最小信号强度: 0.7");
    println!("   - 启用冲突检测");
    println!("   - 启用频率限制");

    // 快速连续生成信号来测试过滤
    let prices = vec![dec!(1.0), dec!(0.95), dec!(0.90), dec!(0.85), dec!(0.88)];
    let mut filtered_count = 0;
    let mut total_generated = 0;

    for (i, price) in prices.iter().enumerate() {
        let candle = Candle {
            timestamp: Utc::now(),
            open: *price,
            high: *price * dec!(1.02),
            low: *price * dec!(0.98),
            close: *price,
            volume: dec!(200),
        };

        let signals = signal_generator.generate_signals(
            &candle, strategy_id, trading_pair.clone()
        )?;

        total_generated += signals.len();

        if !signals.is_empty() {
            filtered_count += signals.len();
            println!("   第 {} 次: 生成 {} 个信号通过过滤", i + 1, signals.len());
            for signal in signals {
                println!("     ✅ {}: 强度 {:.2}", signal.reason, signal.strength.value());
            }
        } else {
            println!("   第 {} 次: 信号被过滤", i + 1);
        }

        // 短暂延迟模拟时间间隔
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
    }

    println!("\n📊 过滤统计:");
    println!("   - 通过过滤的信号: {}", filtered_count);
    println!("   - 过滤效率: {:.1}%", if total_generated > 0 { 
        (filtered_count as f64 / total_generated as f64) * 100.0 
    } else { 
        0.0 
    });

    println!("\n✅ 信号过滤和验证演示完成");
    Ok(())
}
