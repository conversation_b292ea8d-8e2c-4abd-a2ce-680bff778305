//! Diesel 订单仓储实现

use std::sync::Arc;
use diesel::prelude::*;
use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, SigmaXResult, SigmaXError
};

use crate::repositories::traits::OrderRepository;
use crate::diesel_models::*;
use crate::schema::*;
use super::{DbPool, DbConnection};

/// Diesel 订单仓库实现
pub struct DieselOrderRepository {
    pool: Arc<DbPool>,
}

impl DieselOrderRepository {
    pub fn new(pool: Arc<DbPool>) -> Self {
        Self { pool }
    }
    
    fn get_connection(&self) -> SigmaXResult<DbConnection> {
        self.pool.get()
            .map_err(|e| SigmaXError::database_error(format!("Failed to get database connection: {}", e)))
    }
}

impl OrderRepository for DieselOrderRepository {
    async fn save_order(&self, order: &Order) -> SigmaXResult<()> {
        let mut conn = self.get_connection()?;
        let new_order = NewOrder::from(order.clone());
        
        diesel::insert_into(orders::table)
            .values(&new_order)
            .on_conflict(orders::id)
            .do_update()
            .set((
                orders::status.eq(&new_order.status),
                orders::filled_quantity.eq(&new_order.filled_quantity),
                orders::average_price.eq(&new_order.average_price),
                orders::updated_at.eq(&new_order.updated_at),
            ))
            .execute(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to save order: {}", e)))?;
        
        Ok(())
    }
    
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        let mut conn = self.get_connection()?;
        
        let order_model = orders::table
            .filter(orders::id.eq(order_id))
            .first::<OrderModel>(&mut conn)
            .optional()
            .map_err(|e| SigmaXError::database_error(format!("Failed to get order: {}", e)))?;
        
        Ok(order_model.map(|model| model.into()))
    }
    
    async fn get_all_orders(&self) -> SigmaXResult<Vec<Order>> {
        let mut conn = self.get_connection()?;
        
        let order_models = orders::table
            .order(orders::created_at.desc())
            .load::<OrderModel>(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to get all orders: {}", e)))?;
        
        Ok(order_models.into_iter().map(|model| model.into()).collect())
    }
    
    async fn get_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let mut conn = self.get_connection()?;
        let status_str = format!("{:?}", status);
        
        let order_models = orders::table
            .filter(orders::status.eq(status_str))
            .order(orders::created_at.desc())
            .load::<OrderModel>(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to get orders by status: {}", e)))?;
        
        Ok(order_models.into_iter().map(|model| model.into()).collect())
    }
    
    async fn get_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let mut conn = self.get_connection()?;
        
        let order_models = orders::table
            .filter(orders::strategy_id.eq(strategy_id))
            .order(orders::created_at.desc())
            .load::<OrderModel>(&mut conn)
            .map_err(|e| SigmaXError::database_error(format!("Failed to get orders by strategy: {}", e)))?;
        
        Ok(order_models.into_iter().map(|model| model.into()).collect())
    }
}
