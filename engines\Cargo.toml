[package]
name = "sigmax-engines"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
sigmax-core.workspace = true
sigmax-data.workspace = true
sigmax-exchange.workspace = true
sigmax-portfolio.workspace = true
sigmax-execution.workspace = true
sigmax-risk.workspace = true
sigmax-strategies.workspace = true
sigmax-trading-analysis = { path = "../trading_analysis" }
# 新架构依赖
sigmax-interfaces.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
rust_decimal.workspace = true
anyhow.workspace = true
tracing.workspace = true
uuid.workspace = true
async-trait = "0.1"
futures = "0.3"
rand = "0.8"
num_cpus = "1.0"
num-traits = "0.2"
